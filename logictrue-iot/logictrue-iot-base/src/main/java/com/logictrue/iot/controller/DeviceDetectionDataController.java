package com.logictrue.iot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.iot.entity.dto.DeviceDetectionDataDTO;
import com.logictrue.iot.entity.vo.DeviceDetectionDataVO;
import com.logictrue.iot.service.IDeviceDetectionDataService;
import com.logictrue.iot.service.IDeviceDetectionParseService;
import com.logictrue.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 设备检测数据Controller
 *
 */
@Slf4j
@RestController
@RequestMapping("/deviceDetectionData")
@Api(value = "设备检测数据管理", tags = "设备检测数据管理")
@Validated
public class DeviceDetectionDataController {

    @Autowired
    private IDeviceDetectionDataService detectionDataService;

    @Autowired
    private IDeviceDetectionParseService parseService;

    /**
     * 分页查询设备检测数据列表
     */
    @GetMapping("/pageList")
    @ApiOperation(value = "分页查询设备检测数据列表")
    public R<IPage<DeviceDetectionDataVO>> pageList(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Long pageNum,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Long pageSize,
            DeviceDetectionDataDTO query) {
        try {
            Page<DeviceDetectionDataVO> page = new Page<>(pageNum, pageSize);
            IPage<DeviceDetectionDataVO> result = detectionDataService.selectDetectionDataPage(page, query);
            return R.ok(result);
        } catch (Exception e) {
            log.error("分页查询设备检测数据列表失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询检测数据详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询检测数据详情")
    public R<DeviceDetectionDataVO> getDetail(@PathVariable Long id) {
        try {
            DeviceDetectionDataVO result = detectionDataService.getDetectionDataDetail(id);
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询检测数据详情失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 上传并解析Excel文件
     */
    @PostMapping("/upload")
    @ApiOperation(value = "上传并解析Excel文件")
    public R<DeviceDetectionData> uploadExcel(
            @ApiParam(value = "设备编码", required = true) @RequestParam String deviceCode,
            @ApiParam(value = "Excel文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "备注") @RequestParam(required = false) String remark) {
        try {
            // 验证文件
            if (!parseService.validateExcelFile(file)) {
                return R.fail("不支持的文件格式，仅支持 .xlsx 和 .xls 文件");
            }

            DeviceDetectionData result = detectionDataService.uploadAndParseExcel(deviceCode, file, remark);
            return R.ok(result);
        } catch (Exception e) {
            log.error("上传并解析Excel文件失败", e);
            return R.fail("上传失败：" + e.getMessage());
        }
    }

    /**
     * 重新解析Excel文件
     */
    @PostMapping("/reparse/{id}")
    @ApiOperation(value = "重新解析Excel文件")
    public R<String> reparseExcel(@PathVariable Long id) {
        try {
            boolean success = detectionDataService.reparseExcel(id);
            return success ? R.ok("重新解析已开始") : R.fail("重新解析失败");
        } catch (Exception e) {
            log.error("重新解析Excel文件失败", e);
            return R.fail("重新解析失败：" + e.getMessage());
        }
    }

    /**
     * 删除检测数据
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除检测数据")
    public R<String> deleteDetectionData(@PathVariable Long id) {
        try {
            boolean success = detectionDataService.deleteDetectionData(id);
            return success ? R.ok("删除成功") : R.fail("删除失败");
        } catch (Exception e) {
            log.error("删除检测数据失败", e);
            return R.fail("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除检测数据
     */
    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除检测数据")
    public R<String> deleteBatchDetectionData(@RequestBody List<Long> ids) {
        try {
            boolean success = detectionDataService.deleteBatchDetectionData(ids);
            return success ? R.ok("批量删除成功") : R.fail("批量删除失败");
        } catch (Exception e) {
            log.error("批量删除检测数据失败", e);
            return R.fail("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取解析状态统计
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取解析状态统计")
    public R<DeviceDetectionDataVO.ParseStatusStatistics> getStatistics() {
        try {
            DeviceDetectionDataVO.ParseStatusStatistics statistics = detectionDataService.getParseStatusStatistics();
            return R.ok(statistics);
        } catch (Exception e) {
            log.error("获取解析状态统计失败", e);
            return R.fail("获取统计失败：" + e.getMessage());
        }
    }

    /**
     * 根据设备编码获取最新的检测数据
     */
    @GetMapping("/latest/{deviceCode}")
    @ApiOperation(value = "根据设备编码获取最新的检测数据")
    public R<DeviceDetectionDataVO> getLatestByDevice(@PathVariable String deviceCode) {
        try {
            DeviceDetectionDataVO result = detectionDataService.getLatestDetectionDataByDevice(deviceCode);
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取最新检测数据失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 导出检测数据
     */
    @GetMapping("/export/{id}")
    @ApiOperation(value = "导出检测数据")
    public R<String> exportDetectionData(
            @PathVariable Long id,
            @ApiParam(value = "导出格式", example = "excel") @RequestParam(defaultValue = "excel") String format) {
        try {
            detectionDataService.exportDetectionData(id, format);
            return R.ok("导出成功");
        } catch (Exception e) {
            log.error("导出检测数据失败", e);
            return R.fail("导出失败：" + e.getMessage());
        }
    }

    /**
     * 获取支持的文件类型
     */
    @GetMapping("/supportedFileTypes")
    @ApiOperation(value = "获取支持的文件类型")
    public R<String[]> getSupportedFileTypes() {
        try {
            String[] fileTypes = parseService.getSupportedFileTypes();
            return R.ok(fileTypes);
        } catch (Exception e) {
            log.error("获取支持的文件类型失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }
}
