package com.logictrue.iot.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.logictrue.iot.entity.*;
import com.logictrue.iot.mapper.*;
import com.logictrue.iot.service.IDeviceDetectionParseService;
import com.logictrue.iot.service.IDeviceTemplateBindingService;
import com.logictrue.iot.service.IExcelTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备检测数据解析服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class DeviceDetectionParseServiceImpl implements IDeviceDetectionParseService {

    @Value("${file.path:/home/<USER>")
    private String uploadPath;

    @Autowired
    private DeviceDetectionDataMapper detectionDataMapper;

    @Autowired
    private DeviceDetectionBasicFieldMapper basicFieldMapper;

    @Autowired
    private DeviceDetectionTableHeaderMapper tableHeaderMapper;

    @Autowired
    private DeviceDetectionTableDataMapper tableDataMapper;

    @Autowired
    private DeviceDetectionParseLogMapper parseLogMapper;

    @Autowired
    private IDeviceTemplateBindingService bindingService;

    @Autowired
    private IExcelTemplateService templateService;

    @Autowired
    private ExcelTemplateCellMapper templateCellMapper;

    @Autowired
    private ExcelTemplateFieldMapper templateFieldMapper;

    @Autowired
    private ExcelTemplateMapper templateMapper;

    private static final String[] SUPPORTED_FILE_TYPES = {".xlsx", ".xls"};

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceDetectionData parseExcelFile(String deviceCode, MultipartFile file, String remark) {
        log.info("开始解析设备 {} 的Excel文件: {}", deviceCode, file.getOriginalFilename());

        // 1. 验证文件
        if (!validateExcelFile(file)) {
            throw new RuntimeException("不支持的文件格式，仅支持 .xlsx 和 .xls 文件");
        }

        // 2. 获取设备绑定的模板
        Long templateId = bindingService.getTemplateIdByDeviceCode(deviceCode);
        if (templateId == null) {
            throw new RuntimeException("设备 " + deviceCode + " 未绑定Excel模板");
        }

        // 3. 获取模板信息
        ExcelTemplate template = templateService.getById(templateId);
        if (template == null) {
            throw new RuntimeException("模板不存在，ID: " + templateId);
        }

        // 4. 存储上传的文件
        String storedFilePath;
        String timestampedFileName;
        try {
            FileStorageResult storageResult = storeUploadedFile(file);
            storedFilePath = storageResult.getFilePath();
            timestampedFileName = storageResult.getFileName();
            log.info("文件存储成功: {}", storedFilePath);
        } catch (Exception e) {
            log.error("文件存储失败: {}", file.getOriginalFilename(), e);
            throw new RuntimeException("文件存储失败: " + e.getMessage(), e);
        }

        // 5. 创建检测数据记录
        DeviceDetectionData detectionData = new DeviceDetectionData();
        detectionData.setDeviceCode(deviceCode);
        detectionData.setTemplateId(templateId);
        detectionData.setTemplateName(template.getTemplateName());
        detectionData.setFileName(timestampedFileName);
        detectionData.setFilePath(storedFilePath);
        detectionData.setFileSize(file.getSize());
        detectionData.setParseStatus(0); // 待解析
        detectionData.setRemark(remark);

        detectionDataMapper.insert(detectionData);

        // 6. 异步解析文件
        parseExcelFileAsync(detectionData.getId());

        return detectionData;
    }

    /**
     * 文件存储结果内部类
     */
    private static class FileStorageResult {
        private String filePath;
        private String fileName;

        public FileStorageResult(String filePath, String fileName) {
            this.filePath = filePath;
            this.fileName = fileName;
        }

        public String getFilePath() {
            return filePath;
        }

        public String getFileName() {
            return fileName;
        }
    }

    /**
     * 存储上传的文件
     */
    private FileStorageResult storeUploadedFile(MultipartFile file) throws IOException {
        // 1. 生成时间戳文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileExtension = getFileExtension(originalFilename);
        String baseFileName = getBaseFileName(originalFilename);
        String timestampedFileName = baseFileName + "_" + timestamp + "." + fileExtension;

        // 2. 创建日期目录
        String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Path uploadDir = Paths.get(uploadPath, dateDir);

        // 3. 确保目录存在
        if (!Files.exists(uploadDir)) {
            try {
                Files.createDirectories(uploadDir);
                log.info("创建上传目录: {}", uploadDir);
            } catch (IOException e) {
                log.error("创建上传目录失败: {}", uploadDir, e);
                throw new IOException("创建上传目录失败: " + e.getMessage(), e);
            }
        }

        // 4. 检查磁盘空间
        long availableSpace = uploadDir.toFile().getFreeSpace();
        long fileSize = file.getSize();
        if (availableSpace < fileSize * 2) { // 预留双倍空间
            throw new IOException("磁盘空间不足，可用空间: " + availableSpace + " 字节，需要空间: " + (fileSize * 2) + " 字节");
        }

        // 5. 保存文件
        Path targetPath = uploadDir.resolve(timestampedFileName);
        try {
            Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);
            log.info("文件保存成功: {}", targetPath);

            // 6. 验证文件是否保存成功
            if (!Files.exists(targetPath) || Files.size(targetPath) != fileSize) {
                throw new IOException("文件保存验证失败");
            }

            return new FileStorageResult(targetPath.toString(), timestampedFileName);

        } catch (IOException e) {
            // 清理部分写入的文件
            try {
                if (Files.exists(targetPath)) {
                    Files.delete(targetPath);
                }
            } catch (IOException cleanupException) {
                log.warn("清理失败的文件时出错: {}", targetPath, cleanupException);
            }

            log.error("保存文件失败: {}", targetPath, e);
            throw new IOException("保存文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1);
        }
        return "";
    }

    /**
     * 获取基础文件名（不含扩展名）
     */
    private String getBaseFileName(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return filename.substring(0, lastDotIndex);
        }
        return filename;
    }

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void parseExcelFileAsync(Long detectionDataId) {
        DeviceDetectionData detectionData = detectionDataMapper.selectById(detectionDataId);
        if (detectionData == null) {
            log.error("检测数据不存在，ID: {}", detectionDataId);
            return;
        }

        try {
            log.info("开始异步解析检测数据，ID: {}", detectionDataId);

            // 更新解析状态为进行中
            detectionData.setParseStatus(0);
            detectionData.setParseTime(LocalDateTime.now());
            detectionDataMapper.updateById(detectionData);

            // 获取模板配置
            ExcelTemplate template = templateService.getById(detectionData.getTemplateId());
            if (template == null) {
                throw new RuntimeException("模板不存在");
            }

            // 获取模板的单元格配置
            LambdaQueryWrapper<ExcelTemplateCell> cellWrapper = new LambdaQueryWrapper<>();
            cellWrapper.eq(ExcelTemplateCell::getTemplateId, template.getId());
            cellWrapper.orderBy(true, true, ExcelTemplateCell::getSheetIndex, ExcelTemplateCell::getSortOrder);
            List<ExcelTemplateCell> templateCells = templateCellMapper.selectList(cellWrapper);

            // 获取模板的字段配置
            LambdaQueryWrapper<ExcelTemplateField> fieldWrapper = new LambdaQueryWrapper<>();
            fieldWrapper.eq(ExcelTemplateField::getTemplateId, template.getId());
            fieldWrapper.orderBy(true, true, ExcelTemplateField::getSortOrder);
            List<ExcelTemplateField> templateFields = templateFieldMapper.selectList(fieldWrapper);

            // 解析Excel文件
            parseExcelContent(detectionData, template, templateCells, templateFields);

            // 更新解析状态为成功
            detectionData.setParseStatus(1);
            detectionData.setParseMessage("解析成功");
            detectionDataMapper.updateById(detectionData);

            log.info("检测数据解析完成，ID: {}", detectionDataId);

        } catch (Exception e) {
            log.error("解析检测数据失败，ID: {}", detectionDataId, e);

            // 更新解析状态为失败
            detectionData.setParseStatus(2);
            detectionData.setParseMessage("解析失败: " + e.getMessage());
            detectionDataMapper.updateById(detectionData);

            // 记录错误日志
            addParseLog(detectionDataId, "ERROR", "解析失败: " + e.getMessage(), null, null, null);
        }
    }

    /**
     * 解析Excel内容（核心解析逻辑）
     */
    private void parseExcelContent(DeviceDetectionData detectionData, ExcelTemplate template,
                                 List<ExcelTemplateCell> templateCells, List<ExcelTemplateField> templateFields) {
        log.info("开始解析Excel内容，检测数据ID: {}", detectionData.getId());

        try {
            // 1. 读取Excel文件
            Workbook workbook = readExcelFile(detectionData.getFilePath());
            if (workbook == null) {
                throw new RuntimeException("无法读取Excel文件: " + detectionData.getFilePath());
            }

            // 2. 更新总Sheet数量
            int totalSheets = workbook.getNumberOfSheets();
            detectionData.setTotalSheets(totalSheets);

            // 3. 按Sheet分组处理模板配置
            Map<String, List<ExcelTemplateCell>> cellsBySheet = templateCells.stream()
                    .collect(Collectors.groupingBy(cell ->
                        StringUtils.hasText(cell.getSheetId()) ? cell.getSheetId() : "default"));

            Map<String, List<ExcelTemplateField>> fieldsBySheet = templateFields.stream()
                    .collect(Collectors.groupingBy(field ->
                        StringUtils.hasText(field.getSheetId()) ? field.getSheetId() : "default"));

            // 4. 统计信息
            int parsedSheets = 0;
            int basicFieldsCount = 0;
            int tableRowsCount = 0;

            // 5. 处理每个Sheet
            for (int sheetIndex = 0; sheetIndex < totalSheets; sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                String sheetName = sheet.getSheetName();
                String sheetId = getSheetIdByIndex(cellsBySheet, sheetIndex, sheetName);

                List<ExcelTemplateCell> sheetCells = cellsBySheet.getOrDefault(sheetId, new ArrayList<>());
                List<ExcelTemplateField> sheetFields = fieldsBySheet.getOrDefault(sheetId, new ArrayList<>());

                try {
                    // 打印模板配置信息用于调试
                    log.info("Sheet {} 的模板配置: 单元格数量={}, 字段数量={}", sheetName, sheetCells.size(), sheetFields.size());

                    // 解析基础字段
                    int sheetBasicFields = parseBasicFieldsFromSheet(detectionData.getId(), sheet, sheetIndex,
                                                                   sheetId, sheetName, sheetCells, sheetFields);
                    basicFieldsCount += sheetBasicFields;

                    // 解析表格数据
                    int sheetTableRows = parseTableDataFromSheet(detectionData.getId(), sheet, sheetIndex,
                                                               sheetId, sheetName, sheetCells, sheetFields);
                    tableRowsCount += sheetTableRows;

                    parsedSheets++;
                    addParseLog(detectionData.getId(), "INFO",
                        String.format("Sheet %s 解析完成，基础字段: %d，表格行数: %d", sheetName, sheetBasicFields, sheetTableRows),
                        sheetId, sheetName, sheetIndex);

                } catch (Exception e) {
                    log.error("解析Sheet {} 失败", sheetName, e);
                    addParseLog(detectionData.getId(), "ERROR",
                        String.format("Sheet %s 解析失败: %s", sheetName, e.getMessage()),
                        sheetId, sheetName, sheetIndex);
                }
            }

            // 6. 更新统计信息
            detectionData.setParsedSheets(parsedSheets);
            detectionData.setBasicFieldsCount(basicFieldsCount);
            detectionData.setTableRowsCount(tableRowsCount);

            // 7. 关闭工作簿
            workbook.close();

        } catch (Exception e) {
            log.error("解析Excel内容失败", e);
            throw new RuntimeException("解析Excel内容失败: " + e.getMessage(), e);
        }
    }

    /**
     * 读取Excel文件
     */
    private Workbook readExcelFile(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                log.error("Excel文件不存在: {}", filePath);
                return null;
            }

            FileInputStream fis = new FileInputStream(file);
            Workbook workbook;

            // 根据文件扩展名选择合适的工作簿类型
            if (filePath.toLowerCase().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(fis);
            } else if (filePath.toLowerCase().endsWith(".xls")) {
                workbook = new HSSFWorkbook(fis);
            } else {
                fis.close();
                throw new RuntimeException("不支持的文件格式: " + filePath);
            }

            fis.close();
            return workbook;

        } catch (Exception e) {
            log.error("读取Excel文件失败: {}", filePath, e);
            return null;
        }
    }

    /**
     * 根据Sheet索引和名称获取对应的SheetId
     */
    private String getSheetIdByIndex(Map<String, List<ExcelTemplateCell>> cellsBySheet, int sheetIndex, String sheetName) {
        // 优先根据Sheet名称匹配
        for (Map.Entry<String, List<ExcelTemplateCell>> entry : cellsBySheet.entrySet()) {
            List<ExcelTemplateCell> cells = entry.getValue();
            if (!cells.isEmpty()) {
                ExcelTemplateCell firstCell = cells.get(0);
                if (sheetName.equals(firstCell.getSheetName()) ||
                    (firstCell.getSheetIndex() != null && firstCell.getSheetIndex().equals(sheetIndex))) {
                    return entry.getKey();
                }
            }
        }

        // 如果没有匹配的，返回默认值
        return cellsBySheet.containsKey("default") ? "default" :
               (cellsBySheet.isEmpty() ? "default" : cellsBySheet.keySet().iterator().next());
    }

    /**
     * 解析基础字段
     */
    private int parseBasicFields(Long detectionDataId, String sheetId,
                               List<ExcelTemplateCell> sheetCells, List<ExcelTemplateField> sheetFields) {
        // 获取基础字段的单元格配置
        List<ExcelTemplateCell> basicCells = sheetCells.stream()
                .filter(cell -> "basic".equals(cell.getFieldType()) ||
                              ("label".equals(cell.getCellType()) || "value".equals(cell.getCellType())))
                .collect(Collectors.toList());

        // 按字段编码分组
        Map<String, List<ExcelTemplateCell>> cellsByFieldCode = basicCells.stream()
                .filter(cell -> StringUtils.hasText(cell.getFieldCode()))
                .collect(Collectors.groupingBy(ExcelTemplateCell::getFieldCode));

        int count = 0;
        for (Map.Entry<String, List<ExcelTemplateCell>> entry : cellsByFieldCode.entrySet()) {
            String fieldCode = entry.getKey();
            List<ExcelTemplateCell> fieldCells = entry.getValue();

            // 查找对应的字段配置
            ExcelTemplateField fieldConfig = sheetFields.stream()
                    .filter(field -> fieldCode.equals(field.getFieldCode()) && "basic".equals(field.getFieldCategory()))
                    .findFirst()
                    .orElse(null);

            if (fieldConfig != null) {
                // 创建基础字段记录
                DeviceDetectionBasicField basicField = createBasicField(detectionDataId, sheetId, fieldConfig, fieldCells);
                if (basicField != null) {
                    basicFieldMapper.insert(basicField);
                    count++;
                }
            }
        }

        return count;
    }

    /**
     * 创建基础字段记录
     */
    private DeviceDetectionBasicField createBasicField(Long detectionDataId, String sheetId,
                                                     ExcelTemplateField fieldConfig, List<ExcelTemplateCell> fieldCells) {
        DeviceDetectionBasicField basicField = new DeviceDetectionBasicField();
        basicField.setDetectionDataId(detectionDataId);
        basicField.setSheetId(sheetId);
        basicField.setFieldCode(fieldConfig.getFieldCode());
        basicField.setFieldName(fieldConfig.getFieldName());
        basicField.setFieldType(fieldConfig.getFieldType());
        basicField.setSortOrder(fieldConfig.getSortOrder());

        // 查找label和value单元格
        ExcelTemplateCell labelCell = fieldCells.stream()
                .filter(cell -> "label".equals(cell.getCellType()))
                .findFirst()
                .orElse(null);

        ExcelTemplateCell valueCell = fieldCells.stream()
                .filter(cell -> "value".equals(cell.getCellType()))
                .findFirst()
                .orElse(null);

        if (labelCell != null) {
            basicField.setLabelPosition(labelCell.getCellPosition());
            basicField.setLabelRowIndex(labelCell.getRowIndex());
            basicField.setLabelColIndex(labelCell.getColIndex());
        }

        if (valueCell != null) {
            basicField.setValuePosition(valueCell.getCellPosition());
            basicField.setValueRowIndex(valueCell.getRowIndex());
            basicField.setValueColIndex(valueCell.getColIndex());
            // 这里应该从实际Excel文件中读取值，暂时使用模拟数据
            basicField.setFieldValue("模拟值_" + fieldConfig.getFieldCode());
        }

        return basicField;
    }

    /**
     * 从Excel Sheet解析基础字段
     */
    private int parseBasicFieldsFromSheet(Long detectionDataId, Sheet sheet, int sheetIndex,
                                        String sheetId, String sheetName,
                                        List<ExcelTemplateCell> sheetCells, List<ExcelTemplateField> sheetFields) {
        log.info("开始解析基础字段，Sheet: {}, 单元格数量: {}, 字段数量: {}", sheetName, sheetCells.size(), sheetFields.size());

        // 获取基础字段的单元格配置 - 包含label和value类型的单元格
        List<ExcelTemplateCell> basicCells = sheetCells.stream()
                .filter(cell -> "label".equals(cell.getCellType()) || "value".equals(cell.getCellType()))
                .collect(Collectors.toList());

        log.info("找到基础字段单元格数量: {}", basicCells.size());

        // 按基础字段编码分组，处理label和value的关联
        Map<String, List<ExcelTemplateCell>> cellsByBaseFieldCode = new HashMap<>();

        for (ExcelTemplateCell cell : basicCells) {
            if (!StringUtils.hasText(cell.getFieldCode())) {
                continue;
            }

            String baseFieldCode;
            if ("value".equals(cell.getCellType()) && cell.getFieldCode().endsWith("_value")) {
                // value单元格，去掉_value后缀得到基础字段编码
                baseFieldCode = cell.getFieldCode().substring(0, cell.getFieldCode().length() - 6);
            } else if ("label".equals(cell.getCellType())) {
                // label单元格，直接使用字段编码
                baseFieldCode = cell.getFieldCode();
            } else {
                log.warn("未识别的单元格类型或字段编码格式: 类型={}, 字段编码={}", cell.getCellType(), cell.getFieldCode());
                continue;
            }

            cellsByBaseFieldCode.computeIfAbsent(baseFieldCode, k -> new ArrayList<>()).add(cell);
        }

        log.info("按基础字段编码分组后的字段数量: {}", cellsByBaseFieldCode.size());

        int count = 0;
        // 处理每个基础字段的label和value关联
        for (Map.Entry<String, List<ExcelTemplateCell>> entry : cellsByBaseFieldCode.entrySet()) {
            String baseFieldCode = entry.getKey();
            List<ExcelTemplateCell> fieldCells = entry.getValue();

            log.info("处理基础字段: {}, 相关单元格数量: {}", baseFieldCode, fieldCells.size());

            // 查找对应的字段配置（使用基础字段编码）
            ExcelTemplateField fieldConfig = sheetFields.stream()
                    .filter(field -> baseFieldCode.equals(field.getFieldCode()) && "basic".equals(field.getFieldCategory()))
                    .findFirst()
                    .orElse(null);

            if (fieldConfig != null) {
                // 查找label和value单元格
                ExcelTemplateCell labelCell = fieldCells.stream()
                        .filter(cell -> "label".equals(cell.getCellType()))
                        .findFirst().orElse(null);

                ExcelTemplateCell valueCell = fieldCells.stream()
                        .filter(cell -> "value".equals(cell.getCellType()))
                        .findFirst().orElse(null);

                // 创建基础字段记录
                DeviceDetectionBasicField basicField = new DeviceDetectionBasicField();
                basicField.setDetectionDataId(detectionDataId);
                basicField.setSheetId(sheetId);
                basicField.setSheetName(sheetName);
                basicField.setSheetIndex(sheetIndex);
                basicField.setFieldCode(baseFieldCode);
                basicField.setFieldType(fieldConfig.getFieldType());
                basicField.setSortOrder(fieldConfig.getSortOrder());

                // 设置label信息并读取label的实际值作为fieldName
                String fieldName = null;
                if (labelCell != null) {
                    fieldName = getCellValueAsString(sheet, labelCell.getRowIndex(), labelCell.getColIndex());
                    basicField.setLabelPosition(labelCell.getCellPosition());
                    basicField.setLabelRowIndex(labelCell.getRowIndex());
                    basicField.setLabelColIndex(labelCell.getColIndex());
                    log.debug("从label位置 {} 读取到字段名称: '{}'", labelCell.getCellPosition(), fieldName);
                } else {
                    // 如果没有label单元格，使用字段配置中的fieldName作为备用
                    fieldName = fieldConfig.getFieldName();
                    log.warn("基础字段 {} 没有找到label单元格，使用配置中的字段名称: '{}'", baseFieldCode, fieldName);
                }
                basicField.setFieldName(fieldName);

                // 设置value信息并读取实际值
                String fieldValue = null;
                if (valueCell != null) {
                    fieldValue = getCellValueAsString(sheet, valueCell.getRowIndex(), valueCell.getColIndex());
                    basicField.setValuePosition(valueCell.getCellPosition());
                    basicField.setValueRowIndex(valueCell.getRowIndex());
                    basicField.setValueColIndex(valueCell.getColIndex());
                    log.info("从value位置 {} 读取到值: '{}'", valueCell.getCellPosition(), fieldValue);
                } else {
                    log.warn("基础字段 {} 没有找到value单元格", baseFieldCode);
                }

                basicField.setFieldValue(fieldValue);

                basicFieldMapper.insert(basicField);
                count++;

                log.info("成功创建基础字段记录: 字段编码={}, 字段名='{}' (从label读取), 字段值='{}'",
                        baseFieldCode, fieldName, fieldValue);

            } else {
                log.warn("未找到字段配置: 基础字段编码={}", baseFieldCode);
            }
        }

        log.info("Sheet {} 基础字段解析完成，成功解析 {} 个字段", sheetName, count);
        return count;
    }

    /**
     * 从Excel Sheet解析表格数据
     */
    private int parseTableDataFromSheet(Long detectionDataId, Sheet sheet, int sheetIndex,
                                      String sheetId, String sheetName,
                                      List<ExcelTemplateCell> sheetCells, List<ExcelTemplateField> sheetFields) {
        // 获取表头单元格
        List<ExcelTemplateCell> headerCells = sheetCells.stream()
                .filter(cell -> "header".equals(cell.getCellType()) || "header".equals(cell.getFieldType()))
                .sorted(Comparator.comparing(ExcelTemplateCell::getColIndex))
                .collect(Collectors.toList());

        if (headerCells.isEmpty()) {
            return 0;
        }

        // 创建表头记录
        for (int i = 0; i < headerCells.size(); i++) {
            ExcelTemplateCell headerCell = headerCells.get(i);

            // 从Excel中读取表头内容
            String headerContent = getCellValueAsString(sheet, headerCell.getRowIndex(), headerCell.getColIndex());

            DeviceDetectionTableHeader tableHeader = new DeviceDetectionTableHeader();
            tableHeader.setDetectionDataId(detectionDataId);
            tableHeader.setSheetId(sheetId);
            tableHeader.setSheetName(sheetName);
            tableHeader.setSheetIndex(sheetIndex);
            tableHeader.setHeaderName(StringUtils.hasText(headerContent) ? headerContent : headerCell.getContent());
            tableHeader.setHeaderCode(StringUtils.hasText(headerCell.getFieldCode()) ?
                                    headerCell.getFieldCode() : "col_" + i);
            tableHeader.setHeaderPosition(headerCell.getCellPosition());
            tableHeader.setHeaderRowIndex(headerCell.getRowIndex());
            tableHeader.setHeaderColIndex(headerCell.getColIndex());
            tableHeader.setDataType("text");
            tableHeader.setColumnOrder(i);

            tableHeaderMapper.insert(tableHeader);
        }

        // 从Excel中读取表格数据行
        int dataRowCount = 0;
        if (!headerCells.isEmpty()) {
            // 获取表头行索引，数据从下一行开始
            int headerRowIndex = headerCells.get(0).getRowIndex();
            int dataStartRow = headerRowIndex + 1;
            int lastRowNum = sheet.getLastRowNum();

            // 读取数据行直到遇到空行或Sheet结束
            for (int rowIndex = dataStartRow; rowIndex <= lastRowNum; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    break; // 遇到空行，停止读取
                }

                // 检查是否为空行
                boolean isEmptyRow = true;
                Map<String, Object> rowData = new HashMap<>();

                for (int colIndex = 0; colIndex < headerCells.size(); colIndex++) {
                    ExcelTemplateCell headerCell = headerCells.get(colIndex);
                    String headerCode = StringUtils.hasText(headerCell.getFieldCode()) ?
                                      headerCell.getFieldCode() : "col_" + colIndex;

                    String cellValue = getCellValueAsString(sheet, rowIndex, headerCell.getColIndex());
                    rowData.put(headerCode, cellValue);

                    if (StringUtils.hasText(cellValue)) {
                        isEmptyRow = false;
                    }
                }

                // 如果是空行，停止读取
                if (isEmptyRow) {
                    break;
                }

                // 保存数据行
                DeviceDetectionTableData tableData = new DeviceDetectionTableData();
                tableData.setDetectionDataId(detectionDataId);
                tableData.setSheetId(sheetId);
                tableData.setSheetName(sheetName);
                tableData.setSheetIndex(sheetIndex);
                tableData.setRowIndex(rowIndex);
                tableData.setRowData(JSON.toJSONString(rowData));
                tableData.setRowOrder(dataRowCount);

                tableDataMapper.insert(tableData);
                dataRowCount++;
            }
        }

        return dataRowCount;
    }

    /**
     * 添加解析日志
     */
    private void addParseLog(Long detectionDataId, String logLevel, String message,
                           String sheetId, String sheetName, Integer sheetIndex) {
        DeviceDetectionParseLog parseLog = new DeviceDetectionParseLog();
        parseLog.setDetectionDataId(detectionDataId);
        parseLog.setLogLevel(logLevel);
        parseLog.setLogMessage(message);
        parseLog.setSheetId(sheetId);
        // 如果有sheetName和sheetIndex，可以设置到position字段中
        if (StringUtils.hasText(sheetName) && sheetIndex != null) {
            parseLog.setPosition(sheetName + "[" + sheetIndex + "]");
        }

        parseLogMapper.insert(parseLog);
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Sheet sheet, int rowIndex, int colIndex) {
        try {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                return "";
            }

            Cell cell = row.getCell(colIndex);
            if (cell == null) {
                return "";
            }

            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue().toString();
                    } else {
                        // 处理数字，避免科学计数法
                        double numericValue = cell.getNumericCellValue();
                        if (numericValue == (long) numericValue) {
                            return String.valueOf((long) numericValue);
                        } else {
                            return String.valueOf(numericValue);
                        }
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        return cell.getStringCellValue();
                    } catch (Exception e) {
                        try {
                            return String.valueOf(cell.getNumericCellValue());
                        } catch (Exception e2) {
                            return cell.getCellFormula();
                        }
                    }
                case BLANK:
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("读取单元格值失败，行: {}, 列: {}", rowIndex, colIndex, e);
            return "";
        }
    }

    @Override
    public DeviceDetectionData reparseExcelFile(Long detectionDataId) {
        // 重新解析的逻辑
        DeviceDetectionData detectionData = detectionDataMapper.selectById(detectionDataId);
        if (detectionData == null) {
            throw new RuntimeException("检测数据不存在");
        }

        // 清除之前的解析结果
        clearParseResults(detectionDataId);

        // 重新解析
        parseExcelFileAsync(detectionDataId);

        return detectionData;
    }

    /**
     * 清除解析结果
     */
    private void clearParseResults(Long detectionDataId) {
        // 删除基础字段
        LambdaQueryWrapper<DeviceDetectionBasicField> basicWrapper = new LambdaQueryWrapper<>();
        basicWrapper.eq(DeviceDetectionBasicField::getDetectionDataId, detectionDataId);
        basicFieldMapper.delete(basicWrapper);

        // 删除表头
        LambdaQueryWrapper<DeviceDetectionTableHeader> headerWrapper = new LambdaQueryWrapper<>();
        headerWrapper.eq(DeviceDetectionTableHeader::getDetectionDataId, detectionDataId);
        tableHeaderMapper.delete(headerWrapper);

        // 删除表格数据
        LambdaQueryWrapper<DeviceDetectionTableData> dataWrapper = new LambdaQueryWrapper<>();
        dataWrapper.eq(DeviceDetectionTableData::getDetectionDataId, detectionDataId);
        tableDataMapper.delete(dataWrapper);

        // 删除解析日志
        LambdaQueryWrapper<DeviceDetectionParseLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.eq(DeviceDetectionParseLog::getDetectionDataId, detectionDataId);
        parseLogMapper.delete(logWrapper);
    }

    @Override
    public boolean validateExcelFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        String fileName = file.getOriginalFilename();
        if (!StringUtils.hasText(fileName)) {
            return false;
        }

        String extension = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        return Arrays.asList(SUPPORTED_FILE_TYPES).contains(extension);
    }

    @Override
    public String[] getSupportedFileTypes() {
        return SUPPORTED_FILE_TYPES.clone();
    }
}
