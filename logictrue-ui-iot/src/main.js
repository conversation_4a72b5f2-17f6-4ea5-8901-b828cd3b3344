import Vue from 'vue'
import Cookies from 'js-cookie'
import Element from 'element-ui'

import './assets/styles/element-variables.scss';
import '@/assets/styles/index.scss'; // global css

import App from './App'
import store from './store'
import router from './router'
import './assets/icons' // icon
import './permission' // permission control

import { parseTime, resetForm} from './utils/ruoyi'

import request from './utils/request';

Vue.prototype.$api = request;
Vue.prototype.$string = require('./utils/string-utils.js');

// 全局方法挂载
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm


Vue.prototype.msgSuccess = function (msg) {
  this.$message({ showClose: true, message: msg, type: 'success' })
}
Vue.prototype.msgError = function (msg) {
  this.$message({ showClose: true, message: msg, type: 'error' })
}
Vue.prototype.msgInfo = function (msg) {
  this.$message.info(msg)
}

// 全局组件挂载
Vue.component('DictTag', () => import('@/components/DictTag'))

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
