// cover some element-ui styles
@import 'variables';

.el-table--border{
  border-color: #0B4089;
}
.el-input-number.is-controls-right .el-input-number__increase,.el-input-number.is-controls-right .el-input-number__decrease{
  border-color:#025fb2;
}


.el-collapse-item__header,.el-collapse-item__wrap{
  background: transparent;
  color: #fefefe;
}
.el-select-dropdown{
  background:#104895 ;
  border: 1px solid #025fb2;

  .el-select-dropdown__item{
    color: #ccc;
    &:hover,&.hover{
      background: #009BFF;
      color: #fff;
    }
    &.selected{
      color:#fff ;
    }
  }
}
.el-dropdown-menu{
  background:#104895  ;
  .el-dropdown-menu__item{
    color: #fefefe;
  }
}

.el-cascader-node:not(.is-disabled):hover, .el-cascader-node:not(.is-disabled):focus{
  background:#025fb2   ;
}

.el-tree{
  background:transparent ;
  color: #fefefe;
  .el-tree-node__content:hover{
    background:#025fb2 ;
  }
}
.el-tabs--border-card > .el-tabs__header .el-tabs__item:hover{
  color: #fefefe !important;
}
.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active{
  background:#0092f9;
  border-color: #0092f9;
  color: #fefefe;
}
.el-picker-panel,.el-cascader__dropdown, .el-input-group__append,.el-card,.el-calendar__header,.el-picker-panel__footer,.el-drawer,.el-tabs--border-card > .el-tabs__header,.el-tabs--border-card,.el-popover__title,.el-date-table th,.el-picker-panel__sidebar,.el-date-range-picker__content.is-left{
  background:#104895;
  border-color: #025fb2;
  color: #fefefe;
}
.el-popper[x-placement^=bottom] .popper__arrow::after{
  border-color: transparent;
  border-bottom-color:#104895;
}
.el-table--border::after{
  width: 0;
}
.el-table__fixed::before, .el-table__fixed-right::before{
  background: transparent;
}
.el-tabs__item,.el-cascader-menu, .el-date-editor .el-range-separator,.el-checkbox,.el-drawer__header,.el-dialog__close,.el-picker-panel__icon-btn,.el-message-box__content,.el-message-box__title,.el-message-box__close,.el-month-table td .cell,.el-dialog__headerbtn .el-dialog__close,.el-picker-panel__shortcut,.el-date-editor .el-range-input,.el-table__empty-text,.el-pagination__total,.el-pagination__jump,.el-input-number__increase, .el-input-number__decrease,.el-table__expand-icon{
  color: #fefefe;
}
.el-checkbox__inner{
  background: transparent;
}

.el-tag{

background: rgba($color: #009BFF, $alpha: 0.25);
color: #009BFF;
border: none;
  &.el-tag--danger{
    color: #FF3C3C;
    background: rgba($color: #FF3C3C, $alpha: 0.25);
  }
  &.el-tag--warning{
    color: #F2C927;
    background: rgba($color: #F2C927, $alpha: 0.25);
  }
}



.el-button:not(.el-button--text){
background: linear-gradient(0deg, #0096FF, #043475) !important;
border: 1px solid #548DDB !important ;
color: #fefefe !important;
font-size: 16px !important;
}

.el-range-input{
  background: transparent;
}

.el-pagination.is-background .btn-prev, .el-pagination.is-background .btn-next, .el-pagination.is-background .el-pager li,.el-date-table td.in-range div,.el-date-table td.in-range div:hover,.el-month-table td.in-range div:hover,.el-month-table td.in-range div{
  background: #0B4089;
  color: #fefefe;
}
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-loading-mask {
  background-color: transparent !important;
}
.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}



// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-table .el-table__cell {
  background: transparent !important;
  padding: 3px 0;
}

.el-table thead {
  color: #fefefe !important;


  background: linear-gradient(90deg, #0d438d 0%, rgba(#0d438d,0.4) 100%) !important;
  &>tr{
    background-image: url('../images/表头背景.png');
    background-repeat: no-repeat;
    background-position: left ;
    background-size: contain;
    background-color: transparent;
  }

  th {
    display: table-cell;
    font-weight: normal;
  }


}

:root {
  --tableBorder: none;
  // --tableBorder: 1px solid #ebeef5;
}
.el-link{
  font-size: 16px;
  &.el-link--default{
    color: #fefefe;
  }
  &.el-link--primary{
    color: #0093fb;
  }
}

.el-table {
  border-collapse: separate;
  color: #fefefe !important;
  background: transparent;
  font-size: 16px;
  th.el-table__cell.is-leaf, td.el-table__cell{
    border-bottom:2px solid #062769;
  }

  tr {
    height: 56px;
    background: transparent;
  }
  .el-table__cell{
    border-color:#062769 ;
  }

  .el-table__fixed-right {
    height: calc(100% - 16px)  !important;
    right: 16px !important;
  }
  &:before {
    display: none;
  }
  .el-table__row:nth-child(even){
     background:rgba($color:   #0B4089, $alpha: 1) ;

  }
  .el-table__row:nth-child(odd){
    background:rgba($color:   #093882, $alpha: 1) ;

 }
  td {
    .cell {
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;

      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      padding: 0 10px;
      line-height: 50px;
      height: 100%;
      .el-input__inner {
        width: 200px;
      }
    }
  }
}

// 表格无数据的样式
.el-table__empty-text {
  line-height: 10px !important;
}



.el-table__fixed {
  height: 100% !important;
}

.el-message {
  z-index: 9999 !important;
}

//弹出层样式
.el-dialog__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}
.el-message-box,.el-popover{
  background: #104895;
  color: #fefefe;
  border: 1px solid #104895;
}
.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: url('../images/主页背景.png') 100% 100% no-repeat !important;
  background-size: 100% 100%;
  color: #fefefe;
  display: flex;
  border-radius: 5px;
  flex-direction: column;
  margin: 0 !important;
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  -webkit-backface-visibility: hidden;
  overflow: hidden;
  .el-dialog__title{
    color: #fefefe;
  }
}

.el-radio__label{
  color: #fefefe;
}

.el-dialog__body,
.el-dialog--center .el-dialog__body {
  flex: 1;
  padding: 20px 32px !important;
  color: #fefefe;
}

.el-dialog .el-dialog__header {
  padding-top: 14px;
  padding-bottom: 0px;
  text-align: center;
}

.el-dialog__footer {
  text-align: center !important;
  padding-bottom: 30px !important;
}

.el-dialog__title {
  font-size: 18px !important;
  font-weight: bold !important;
  color: #364760;
}

.el-dialog-btn {
  width: 170px !important;
  height: 34px !important;
  border-radius: 20px !important;
}

.el-add-btn {
  width: 90px !important;
  height: 36px !important;
  border-radius: 20px;
}



.el-radio__input.is-disabled.is-checked {
  .el-radio__inner {
    background-color: #1890ff;
    border-color: #1890ff;

    &:after {
      background-color: #FFFFFF;
    }
  }

  &+span.el-radio__label {
    color: #1890ff;
  }
}

.el-checkbox__input.is-disabled.is-checked {
  .el-checkbox__inner {
    background-color: #1890ff;
    border-color: #1890ff;

    &:after {
      border-color: #FFFFFF;
    }
  }

  &+.el-checkbox__label {
    color: #1890ff;
  }
}

// 时间选择器
.el-range-editor.is-disabled {
  background-color: #fff;

  input {
    background-color: #fff;
    color: #606266;
  }
}

// 多选
.el-checkbox__input.is-disabled .el-checkbox__inner {
  background-color: transparent;
}







.gutter {
  display: block !important;
}

.el-table__body-wrapper::-webkit-scrollbar,
textarea::-webkit-scrollbar {
  width: $tablleScroll; // 横向滚动条
  height: $tablleScroll; // 纵向滚动条 必写
}

textarea::-webkit-scrollbar {
  width: $tablleScroll; // 横向滚动条
  height: $tablleScroll; // 纵向滚动条 必写
}

.el-table__empty-block {
  height: 130px !important;
}

// 滚动条的滑块
.el-table__body-wrapper::-webkit-scrollbar-thumb,
textarea::-webkit-scrollbar-thumb {
  background-color: #70a7cb;
  border-radius: 3px;
}


.el-drawer__wrapper {
  z-index: 2002 !important;
}

.el-notification {
  z-index: 2003 !important;
}

// 日历组件样式
.el-calendar__header {
  border-radius: 5px 5px 0 0;
}

.el-calendar__button-group {
  margin-right: 10px;
}

.field-wrapper .el-form-item {
  margin: 0;
}

.el-form-item .el-form-item__label {
  font-size: 16px;
  color: #fefefe;
  font-weight: normal !important;
}

.el-input__inner,.vue-treeselect__control,.el-textarea__inner {

  background: linear-gradient(0deg, #0D3A8D, #03256B) !important;
  border: 1px solid #3E66C2 !important;
  color: #fefefe !important;
  border-radius: 4px;

}
.vue-treeselect__single-value{
  color: #fefefe !important;
}

.el-form-item__content {
  .el-input--medium {
    .el-input-group__append {
      height: 37px !important;
    }
  }
}

body .el-table th.gutter {
  display: table-cell !important
}



/* 设置全局 */
.el-input::-webkit-outer-spin-button,
.el-input::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none !important;
}

.el-input input[type="number"]::-webkit-outer-spin-button,
.el-input input[type="number"]::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none !important;
}

.el-input {
  -moz-appearance: textfield;
}

.el-input input[type="number"] {
  -moz-appearance: textfield;
}

textarea {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

.el-tooltip__popper {
  background: #017cda !important;

  &[x-placement^="bottom"] {
    .popper__arrow::after {
      border-bottom-color: #017cda !important;
    }
  }

  &[x-placement^="top"] {
    .popper__arrow::after {
      border-top-color: #017cda !important;
    }
  }

  &[x-placement^="right"] {
    .popper__arrow::after {
      border-right-color: #017cda !important;
    }
  }

  &[x-placement^="left"] {
    .popper__arrow::after {
      border-left-color: #017cda !important;
    }
  }

}


.el-table__fixed-header-wrapper tr{
  background: #0b3982 !important;
}
