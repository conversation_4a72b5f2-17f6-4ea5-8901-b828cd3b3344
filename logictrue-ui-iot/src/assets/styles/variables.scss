// base color
$blue: #324157;
$light-blue: #1890FF;
$red: #C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow: #FEC171;
$panGreen: #30B08F;
// 品牌色
$brand_blue: #1890FF;

// sidebar
$menuText: #ffffff;
$menuActiveText: #ffffff;
$subMenuActiveText: #f4f4f5; // https://github.com/ElemeFE/element/issues/12951

$menuBg: #030021;;
$menuHover: RGB(44,41,69);
$sidebarTitle: #ffffff;

$menuLightBg: #ffffff;
$menuLightHover: #f0f1f5;
$sidebarLightTitle: #001529;

$subMenuBg: #1f2d3d;
$subMenuHover: #001528;

$sideBarWidth: 240px;

$mainColor: #1890FF;
$menuActiveBgColor: RGB(44,41,69); //鼠标悬浮|选中 背景色
$tablleScroll: 16px;
$baseHeight: calc(100vh - 125px);

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  menuLightBg: $menuLightBg;
  menuLightHover: $menuLightHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  sidebarTitle: $sidebarTitle;
  sidebarLightTitle: $sidebarLightTitle;
  tablleScroll: $tablleScroll
}
