<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板编码" prop="templateCode">
        <el-input
          v-model="queryParams.templateCode"
          placeholder="请输入模板编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备类型" prop="deviceType">
        <el-input
          v-model="queryParams.deviceType"
          placeholder="请输入设备类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-document-copy"
          size="mini"
          :disabled="single"
          @click="handleCopy"
        >复制</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-link"
          size="mini"
          @click="handleBindingManage"
        >绑定管理</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="模板名称" align="center" prop="templateName" />
      <el-table-column label="模板编码" align="center" prop="templateCode" />
      <el-table-column label="设备类型" align="center" prop="deviceType" />
      <el-table-column label="列数" align="center" prop="maxColumns" />
      <el-table-column label="行数" align="center" prop="maxRows" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDesign(scope.row)"
          >设计</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleExport(scope.row)"
          >导出</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <lt-pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      layout="total,prev, pager, next"
      @pagination="getList"
    />

    <!-- 添加或修改模板对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model="form.templateName" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="模板编码" prop="templateCode">
          <el-input v-model="form.templateCode" placeholder="请输入模板编码" />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-input v-model="form.deviceType" placeholder="请输入设备类型" />
        </el-form-item>
        <el-form-item label="模板描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入模板描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 复制模板对话框 -->
    <el-dialog title="复制模板" :visible.sync="copyOpen" width="400px" append-to-body>
      <el-form ref="copyForm" :model="copyForm" :rules="copyRules" label-width="80px">
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model="copyForm.templateName" placeholder="请输入新模板名称" />
        </el-form-item>
        <el-form-item label="模板编码" prop="templateCode">
          <el-input v-model="copyForm.templateCode" placeholder="请输入新模板编码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCopy">确 定</el-button>
        <el-button @click="cancelCopy">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listExcelTemplate,
  getExcelTemplate,
  deleteExcelTemplate,
  saveExcelTemplate,
  copyExcelTemplate,
  updateExcelTemplateStatus,
  exportBlankExcelTemplate
} from '@/api/analysis/excelTemplate'
import ltPagination from "../../../components/lt-pagination/lt-pagination.vue"
import rightToolbar from "../../../components/RightToolbar/index.vue";

export default {
  name: 'ExcelTemplateList',
  components: { ltPagination, rightToolbar },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 模板表格数据
      templateList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示复制弹出层
      copyOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateName: null,
        templateCode: null,
        deviceType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 复制表单参数
      copyForm: {},
      // 表单校验
      rules: {
        templateName: [
          { required: true, message: '模板名称不能为空', trigger: 'blur' }
        ],
        templateCode: [
          { required: true, message: '模板编码不能为空', trigger: 'blur' }
        ]
      },
      // 复制表单校验
      copyRules: {
        templateName: [
          { required: true, message: '模板名称不能为空', trigger: 'blur' }
        ],
        templateCode: [
          { required: true, message: '模板编码不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询模板列表 */
    getList() {
      this.loading = true
      listExcelTemplate(this.queryParams).then(response => {
        if (response.code === 200) {
          this.templateList = response.data.records || []
          this.total = response.data.total || 0
        } else {
          this.templateList = []
          this.total = 0
          this.$message.error(response.msg || '查询失败')
        }
        this.loading = false
      }).catch(error => {
        this.templateList = []
        this.total = 0
        this.loading = false
        this.$message.error('查询失败: ' + (error.message || '网络错误'))
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 取消复制按钮
    cancelCopy() {
      this.copyOpen = false
      this.resetCopy()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        templateName: null,
        templateCode: null,
        deviceType: null,
        description: null,
        maxColumns: 20,
        maxRows: 30,
        status: 1
      }
      this.resetForm('form')
    },
    // 复制表单重置
    resetCopy() {
      this.copyForm = {
        templateName: null,
        templateCode: null
      }
      this.resetForm('copyForm')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加Excel模板'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getExcelTemplate(id).then(response => {
        if (response.code === 200) {
          this.form = response.data
          this.open = true
          this.title = '修改Excel模板'
        } else {
          this.$message.error(response.msg || '获取模板信息失败')
        }
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            saveExcelTemplate(this.form).then(response => {
              if (response.code === 200) {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              } else {
                this.$modal.msgError(response.msg || '修改失败')
              }
            })
          } else {
            saveExcelTemplate(this.form).then(response => {
              if (response.code === 200) {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              } else {
                this.$modal.msgError(response.msg || '新增失败')
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除模板编号为"' + ids + '"的数据项？').then(function() {
        return deleteExcelTemplate(ids)
      }).then((response) => {
        if (response.code === 200) {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        } else {
          this.$modal.msgError(response.msg || '删除失败')
        }
      }).catch(() => {})
    },
    /** 复制按钮操作 */
    handleCopy(row) {
      this.resetCopy()
      this.copyForm.sourceId = row.id
      this.copyForm.templateName = row.templateName + '_副本'
      this.copyForm.templateCode = row.templateCode + '_COPY'
      this.copyOpen = true
    },
    /** 提交复制 */
    submitCopy() {
      this.$refs['copyForm'].validate(valid => {
        if (valid) {
          copyExcelTemplate(
            this.copyForm.sourceId,
            this.copyForm.templateName,
            this.copyForm.templateCode
          ).then(response => {
            this.$modal.msgSuccess('复制成功')
            this.copyOpen = false
            this.getList()
          })
        }
      })
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === 1 ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '""' + row.templateName + '"模板吗？').then(function() {
        return updateExcelTemplateStatus(row.id, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function() {
        row.status = row.status === 0 ? 1 : 0
      })
    },
    /** 设计按钮操作 */
    handleDesign(row) {
      this.$router.push({
        path: '/detection/template/excel-designer',
        query: { templateId: row.id }
      })
    },
    /** 导出按钮操作 */
    handleExport(row) {
      this.$modal.confirm('确认导出"' + row.templateName + '"模板？').then(() => {
        return exportBlankExcelTemplate(row.id)
      }).then(response => {
        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })

        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${row.templateName}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$modal.msgSuccess('导出成功')
      }).catch(() => {})
    },
    /** 绑定管理按钮操作 */
    handleBindingManage() {
      this.$router.push('/detection/template/device-template-binding')
    }
  }
}
</script>
