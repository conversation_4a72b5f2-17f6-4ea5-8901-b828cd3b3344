<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备编码" prop="deviceCode">
        <el-input
          v-model="queryParams.deviceCode"
          placeholder="请输入设备编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
          v-model="queryParams.deviceName"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="绑定状态" prop="bindStatus">
        <el-select v-model="queryParams.bindStatus" placeholder="请选择绑定状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增绑定</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="bindingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备编码" align="center" prop="deviceCode" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="设备类型" align="center" prop="deviceType" />
      <el-table-column label="模板名称" align="center" prop="templateName" />
      <el-table-column label="模板编码" align="center" prop="templateCode" />
      <el-table-column label="绑定状态" align="center" prop="bindStatus">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.bindStatus"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleUnbind(scope.row)"
          >解绑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改绑定对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备编码" prop="deviceCode">
          <el-select
            v-model="form.deviceCode"
            placeholder="请选择设备"
            filterable
            :disabled="form.id != null"
            style="width: 100%"
          >
            <el-option
              v-for="option in availableDeviceOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模板" prop="templateId">
          <el-select
            v-model="form.templateId"
            placeholder="请选择模板"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="template in templateOptions"
              :key="template.id"
              :label="`${template.templateName} (${template.templateCode})`"
              :value="template.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDeviceTemplateBinding,
  createDeviceTemplateBinding,
  updateDeviceTemplateBinding,
  deleteDeviceTemplateBinding,
  deleteBatchDeviceTemplateBinding,
  updateDeviceTemplateBindingStatus,
  getAvailableDeviceCodes,
  getAvailableDeviceOptions,
  unbindTemplate
} from '@/api/analysis/deviceTemplateBinding'
import { listExcelTemplate } from '@/api/analysis/excelTemplate'

export default {
  name: 'DeviceTemplateBinding',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 绑定表格数据
      bindingList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceCode: null,
        deviceName: null,
        templateName: null,
        bindStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deviceCode: [
          { required: true, message: '设备编码不能为空', trigger: 'change' }
        ],
        templateId: [
          { required: true, message: '模板不能为空', trigger: 'change' }
        ]
      },
      // 可用设备编码列表
      availableDeviceCodes: [],
      // 可用设备选项列表（设备名称-设备编码格式）
      availableDeviceOptions: [],
      // 模板选项列表
      templateOptions: []
    }
  },
  created() {
    this.getList()
    this.getAvailableDeviceCodes()
    this.getAvailableDeviceOptions()
    this.getTemplateOptions()
  },
  methods: {
    /** 查询绑定列表 */
    getList() {
      this.loading = true
      listDeviceTemplateBinding(this.queryParams).then(response => {
        if (response.code === 200) {
          this.bindingList = response.data.records
          this.total = response.data.total
        } else {
          this.$message.error(response.msg || '查询失败')
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 获取可用设备编码列表 */
    getAvailableDeviceCodes() {
      getAvailableDeviceCodes().then(response => {
        if (response.code === 200) {
          this.availableDeviceCodes = response.data || []
        }
      })
    },
    /** 获取可用设备选项列表（设备名称-设备编码格式） */
    getAvailableDeviceOptions() {
      getAvailableDeviceOptions().then(response => {
        if (response.code === 200) {
          this.availableDeviceOptions = response.data || []
        }
      })
    },
    /** 获取模板选项列表 */
    getTemplateOptions() {
      listExcelTemplate({ pageNum: 1, pageSize: 1000, status: 1 }).then(response => {
        if (response.code === 200) {
          this.templateOptions = response.data.records || []
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        deviceCode: null,
        templateId: null,
        remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加设备模板绑定'
      this.getAvailableDeviceCodes() // 刷新可用设备列表
      this.getAvailableDeviceOptions() // 刷新可用设备选项列表
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids[0]
      const binding = this.bindingList.find(item => item.id === id)
      if (binding) {
        this.form = { ...binding }
        this.open = true
        this.title = '修改设备模板绑定'
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDeviceTemplateBinding(this.form).then(response => {
              if (response.code === 200) {
                this.$message.success('修改成功')
                this.open = false
                this.getList()
                this.getAvailableDeviceCodes()
                this.getAvailableDeviceOptions()
              } else {
                this.$message.error(response.msg || '修改失败')
              }
            })
          } else {
            createDeviceTemplateBinding(this.form).then(response => {
              if (response.code === 200) {
                this.$message.success('新增成功')
                this.open = false
                this.getList()
                this.getAvailableDeviceCodes()
                this.getAvailableDeviceOptions()
              } else {
                this.$message.error(response.msg || '新增失败')
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id != null ? [row.id] : this.ids
      const deviceCodes = row.deviceCode != null ? [row.deviceCode] :
        this.bindingList.filter(item => ids.includes(item.id)).map(item => item.deviceCode)

      this.$confirm(`是否确认删除设备编码为"${deviceCodes.join(', ')}"的绑定数据项？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (ids.length === 1) {
          deleteDeviceTemplateBinding(ids[0]).then(response => {
            if (response.code === 200) {
              this.$message.success('删除成功')
              this.getList()
              this.getAvailableDeviceCodes()
              this.getAvailableDeviceOptions()
            } else {
              this.$message.error(response.msg || '删除失败')
            }
          })
        } else {
          deleteBatchDeviceTemplateBinding(ids).then(response => {
            if (response.code === 200) {
              this.$message.success('删除成功')
              this.getList()
              this.getAvailableDeviceCodes()
              this.getAvailableDeviceOptions()
            } else {
              this.$message.error(response.msg || '删除失败')
            }
          })
        }
      })
    },
    /** 状态修改 */
    handleStatusChange(row) {
      const text = row.bindStatus === 1 ? '启用' : '禁用'
      this.$confirm(`确认要"${text}""${row.deviceCode}"的绑定吗？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateDeviceTemplateBindingStatus(row.id, row.bindStatus).then(response => {
          if (response.code === 200) {
            this.$message.success(`${text}成功`)
          } else {
            this.$message.error(response.msg || `${text}失败`)
            // 恢复原状态
            row.bindStatus = row.bindStatus === 1 ? 0 : 1
          }
        }).catch(() => {
          // 恢复原状态
          row.bindStatus = row.bindStatus === 1 ? 0 : 1
        })
      }).catch(() => {
        // 恢复原状态
        row.bindStatus = row.bindStatus === 1 ? 0 : 1
      })
    },
    /** 解绑操作 */
    handleUnbind(row) {
      this.$confirm(`是否确认解绑设备"${row.deviceCode}"的模板绑定？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        unbindTemplate(row.deviceCode).then(response => {
          if (response.code === 200) {
            this.$message.success('解绑成功')
            this.getList()
            this.getAvailableDeviceCodes()
            this.getAvailableDeviceOptions()
          } else {
            this.$message.error(response.msg || '解绑失败')
          }
        })
      })
    }
  }
}
</script>
