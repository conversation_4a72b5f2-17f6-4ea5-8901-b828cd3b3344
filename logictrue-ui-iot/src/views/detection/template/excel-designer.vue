<template>
  <div class="app-container">
    <!-- Excel设计器组件 -->
    <ExcelStyleTemplateDesigner
      :template-id="templateId"
      @save="handleSave"
    />
  </div>
</template>

<script>
import ExcelStyleTemplateDesigner from '@/components/detection/ExcelStyleTemplateDesigner'

export default {
  name: 'ExcelDesigner',
  components: {
    ExcelStyleTemplateDesigner
  },
  data() {
    return {
      templateId: null
    }
  },
  created() {
    // 从路由参数获取模板ID
    this.templateId = this.$route.query.templateId || null
  },

  watch: {
    // 监听路由变化，更新templateId
    '$route.query.templateId': {
      handler: function(newId) {
        console.log('路由参数templateId变化:', newId)
        this.templateId = newId || null
      },
      immediate: false
    }
  },
  methods: {
    // 保存成功回调
    handleSave(templateData) {
      console.log('模板保存成功:', templateData)
      this.$message.success('模板保存成功')

      // 可以选择保存后返回列表页
      // this.handleBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

</style>
