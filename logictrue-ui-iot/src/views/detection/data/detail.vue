<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
      <h2>检测数据详情</h2>
    </div>

    <div v-loading="loading">
      <!-- 基本信息 -->
      <el-card class="mb20">
        <div slot="header" class="card-header">
          <span>基本信息</span>
          <div>
            <el-tag :type="getStatusType(detailData.parseStatus)">
              {{ getStatusText(detailData.parseStatus) }}
            </el-tag>
            <el-button 
              type="primary" 
              size="mini" 
              v-if="detailData.parseStatus === 2"
              @click="handleReparse"
              style="margin-left: 10px;"
            >
              重新解析
            </el-button>
          </div>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>文件名：</label>
              <span>{{ detailData.fileName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>设备编码：</label>
              <span>{{ detailData.deviceCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>设备名称：</label>
              <span>{{ detailData.deviceName }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>模板名称：</label>
              <span>{{ detailData.templateName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>文件大小：</label>
              <span>{{ formatFileSize(detailData.fileSize) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>解析时间：</label>
              <span>{{ parseTime(detailData.parseTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>Sheet数量：</label>
              <span>{{ detailData.totalSheets }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>基础字段数：</label>
              <span>{{ detailData.basicFieldsCount }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>表格行数：</label>
              <span>{{ detailData.tableRowsCount }}</span>
            </div>
          </el-col>
        </el-row>
        
        <div class="info-item" v-if="detailData.remark">
          <label>备注：</label>
          <span>{{ detailData.remark }}</span>
        </div>
        
        <div class="info-item" v-if="detailData.parseMessage">
          <label>解析消息：</label>
          <span :class="{'error-message': detailData.parseStatus === 2}">{{ detailData.parseMessage }}</span>
        </div>
      </el-card>

      <!-- Sheet数据 -->
      <el-card v-if="detailData.sheetDataList && detailData.sheetDataList.length > 0">
        <div slot="header" class="card-header">
          <span>解析数据</span>
          <el-button type="primary" size="mini" @click="handleExport">导出数据</el-button>
        </div>

        <!-- Sheet切换标签 -->
        <el-tabs v-model="activeSheet" v-if="detailData.sheetDataList.length > 1">
          <el-tab-pane 
            v-for="(sheet, index) in detailData.sheetDataList" 
            :key="index"
            :label="sheet.sheetName || `Sheet${sheet.sheetIndex + 1}`"
            :name="index.toString()"
          >
            <sheet-data-view :sheet-data="sheet" />
          </el-tab-pane>
        </el-tabs>

        <!-- 单个Sheet直接显示 -->
        <sheet-data-view 
          v-else-if="detailData.sheetDataList.length === 1"
          :sheet-data="detailData.sheetDataList[0]" 
        />
      </el-card>

      <!-- 无数据提示 -->
      <el-card v-else-if="!loading">
        <div class="no-data">
          <i class="el-icon-document"></i>
          <p>暂无解析数据</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getDeviceDetectionData, reparseExcelFile, exportDetectionData } from '@/api/analysis/deviceDetectionData'
import SheetDataView from '@/components/detection/SheetDataView'

export default {
  name: 'DetectionDataDetail',
  components: {
    SheetDataView
  },
  data() {
    return {
      loading: true,
      detailData: {},
      activeSheet: '0'
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    /** 获取详情数据 */
    getDetail() {
      const id = this.$route.params.id
      if (!id) {
        this.$message.error('参数错误')
        this.goBack()
        return
      }

      this.loading = true
      getDeviceDetectionData(id).then(response => {
        if (response.code === 200) {
          this.detailData = response.data || {}
        } else {
          this.$message.error(response.msg || '获取详情失败')
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 返回 */
    goBack() {
      this.$router.go(-1)
    },
    /** 重新解析 */
    handleReparse() {
      this.$confirm('确认要重新解析该文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        reparseExcelFile(this.detailData.id).then(response => {
          if (response.code === 200) {
            this.$message.success('重新解析已开始')
            // 刷新页面数据
            setTimeout(() => {
              this.getDetail()
            }, 2000)
          } else {
            this.$message.error(response.msg || '重新解析失败')
          }
        })
      })
    },
    /** 导出数据 */
    handleExport() {
      this.$confirm('确认要导出解析结果吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        exportDetectionData(this.detailData.id, 'excel').then(response => {
          this.$message.success('导出成功')
        }).catch(() => {
          this.$message.error('导出失败')
        })
      })
    },
    /** 格式化文件大小 */
    formatFileSize(size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return size.toFixed(1) + units[index]
    },
    /** 获取状态类型 */
    getStatusType(status) {
      const statusMap = {
        0: 'warning',
        1: 'success',
        2: 'danger'
      }
      return statusMap[status] || 'info'
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        0: '待解析',
        1: '解析成功',
        2: '解析失败'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style scoped>
.app-container {
  background: linear-gradient(180deg, #124B9A, #0D438D);
  min-height: 100vh;
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 0 10px;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
  min-width: 80px;
  margin-right: 10px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.info-item span {
  color: #ffffff;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.error-message {
  color: #FFB3B3;
  font-weight: 600;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: rgba(255, 255, 255, 0.7);
}

.no-data i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
  color: rgba(255, 255, 255, 0.5);
}

.mb20 {
  margin-bottom: 20px;
}

/* 覆盖Element UI卡片样式 */
:deep(.el-card) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

:deep(.el-card__header) {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

:deep(.el-card__body) {
  color: #ffffff;
}

/* 覆盖标签页样式 */
:deep(.el-tabs__header) {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px 8px 0 0;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: rgba(255, 255, 255, 0.2);
}

:deep(.el-tabs__item) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-tabs__item.is-active) {
  color: #ffffff;
  font-weight: 600;
}

/* 覆盖表格样式 */
:deep(.el-table) {
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
}

:deep(.el-table th) {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.2);
}

:deep(.el-table td) {
  border-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

:deep(.el-table--border::after) {
  background-color: rgba(255, 255, 255, 0.2);
}

:deep(.el-table::before) {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>
