<template>
  <div class="container">
    <div class="top d-flex j-sb a-center">
      <img src="@/assets/favicon.png" style="width: 120px; height: 80px" />
      <dv-border-box-8 style="width: 300px">
        <span>{{ item.car_code }}</span>
      </dv-border-box-8>
      <el-button type="primary" @click="$router.go(-1)">返回</el-button>
    </div>

    <div class="bottom" style="display: block">
      <dv-border-box-12 style="width: 100%; height:150px">
        <p class="title">车辆信息</p>
        <div
          style="height: calc(100% - 30px); width: 100%"
          class="d-flex flex-column j-center"
        >
          <el-descriptions :column="4" border>
            <el-descriptions-item
              label-class-name="d"
              content-class-name="s"
              label="车型"
            >
              {{ item.name }}
            </el-descriptions-item>
            <el-descriptions-item
              label-class-name="d"
              content-class-name="s"
              label="投产状态"
            >
              {{ item.status }}
            </el-descriptions-item>
            <el-descriptions-item
              label-class-name="d"
              content-class-name="s"
              label="下达时间"
            >
              {{ item.issue_time }}
            </el-descriptions-item>
            <el-descriptions-item
              label-class-name="d"
              content-class-name="s"
              label="入厂时间"
            >
              {{ item.input_factory_time }}
            </el-descriptions-item>
            <el-descriptions-item
              label-class-name="d"
              content-class-name="s"
              label="产值"
            >
              {{ item.out_value }}
            </el-descriptions-item>
            <el-descriptions-item
              label-class-name="d"
              content-class-name="s"
              label="维修周期"
            >
              {{ item.repair_month }}
            </el-descriptions-item>
            <el-descriptions-item
              label-class-name="d"
              content-class-name="s"
              label="维修内容"
            >
              {{ item.content }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </dv-border-box-12>

      <div style="width: 100%; height: auto; margin: 10px 0">
        <p class="title">总采集数据</p>
        <div style="height: calc(100% - 30px); width: 100%">
          <div class="table" style="height:auto">
            <div class="item" style="background: #0a73ff">
              <div>序号</div>
              <div>设备名称</div>
              <div>设备编码</div>
              <div>检查时间</div>
              <div>结果</div>
              <div>检查人</div>
            </div>
            <div class="list">
              <div class="item" v-for="(item, index) in detail" :key="index">
                <div>{{ index + 1 }}</div>
                <div>{{ item.device_name }}</div>
                <div>{{ item.device_code }}</div>
                <div>{{ item.check_time }}</div>
                <div>{{ item.result }}</div>
                <div>{{ item.operator }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="width: 100%;height:auto">

          <div v-for="item in detail" style="border-top:2px solid #eee" :key="item.device_code">
            <p class="title mr-b10">{{ item.device_name }}</p>
            <el-descriptions :column="3" border>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="设备编码"
              >
                {{ item.device_code }}
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="有无监控"
              >
                {{ item.is_camera }}
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="检查时间"
              >
                {{ item.check_time }}
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="检查人"
              >
                {{ item.operator }}
              </el-descriptions-item>
              
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="结果"
              >
                {{ item.result }}
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="d"
                content-class-name="s"
                label="备注"
              >
                {{ item.device_remark }}
              </el-descriptions-item>
            </el-descriptions>
            <div style="margin: 10px 0">
              <p class="title">采集数据</p>
              <div style="width: 100%">
                <div class="table" style="height:auto">
                  <div class="item" style="background: #0a73ff">
                    <div>序号</div>
                    <div>项目</div>
                    <div>说明</div>
                    <div>数据</div>
                    <div>结果</div>
                  </div>
                  <div class="list">
                    <div
                      class="item"
                      v-for="(item, index) in item.data"
                      :key="index"
                    >
                      <div>{{ index + 1 }}</div>
                      <div>{{ item.item }}</div>
                      <div>{{ item.item_explain }}</div>
                      <div>{{ item.data }}</div>
                      <div>{{ item.result }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      item: {},
      detail: [],
    };
  },
  methods: {
    getHistoryList() {
      this.$api({
        url: "/interfaces/collect/car/getDeviceCollectByCarCode",
        params: {
          carCode: this.item.car_code,
        },
      }).then((res) => {
        this.detail = res.data;
      });
    },
  },
  mounted() {
    let item = this.$route.params.item;
    this.item = item;
    this.getHistoryList();
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: #041a40;
  padding: 5px 15px;
  width: 100%;
  height: 100%;
  color: #fff;
  overflow: auto;
  .top {
    height: 10%;
    span {
      color: #fff;
      font-size: 26px;
      font-weight: 600;
    }
    ::v-deep .dv-border-box-8 .border-box-content {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .bottom {
    margin-top: 5px;
    height: 89%;
    width: 100%;
    display: flex;
    .table {
      width: 100%;
      color: #fff;
      height: calc(100% - 30px);
      margin-top: 5px;
      display: flex;
      flex-direction: column;
      font-size: 14px;
      .item {
        width: 100%;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 5px 0;
        &:nth-of-type(odd) {
          background: #082c61;
        }
        &:nth-of-type(even) {
          background: #13487f;
        }
        & > div {
          flex: 1;
          text-align: center;
        }
      }

      .list {
        flex: 1;
        overflow: auto;
      }
    }
  }
}
::v-deep .border-box-content {
  padding: 12px;
  // overflow: auto;
}
p {
  text-align: center;
  &.title {
    color: #7ce7fd;
    font-size: 20px;
    margin: 0;
  }
}
::v-deep .d {
  background: #082c61;
  text-align: center;
  color: #fff;
}

::v-deep .s {
  color: #fff;
  background: #13487f;
  text-align: center;
}
::v-deep .echarts {
  width: 100%;
  height: 100%;
}
</style>