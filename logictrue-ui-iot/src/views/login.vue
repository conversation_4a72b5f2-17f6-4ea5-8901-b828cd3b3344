<template>
  <div class="container">
    <div
      class="login"
      :style="{
        backgroundImage: sysConfig.subjectImage
          ? `url(${sysConfig.subjectImage})`
          : `url(${require('../assets/images/iot.png')})`,
      }"
    >
      <div class="login-box">
        <div class="d-flex j-center">
          <img
            src="../assets/images/logo.png"
            style="width: 112px; height: 50px"
            alt=""
          />
        </div>
        <div class="mr-t15 text">{{ sysConfig.title }}</div>
        <div class="mr-t15 text">{{ sysConfig.abbreviation }}</div>
        <div class="d-flex j-center">
          <img
            src="../assets/images/标题装饰.png"
            style="width: 320px; height: 17px"
            alt=""
          />
        </div>
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          style="width: 100%"
          class="mr-t15"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              type="text"
              auto-complete="off"
              placeholder="账号"
              style="width: 100%"
            >
              <img
                slot="prefix"
                src="../assets/images/用户名.png"
                class="mr-t16"
                style="width: 24px; height: 24px"
              />
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              auto-complete="off"
              placeholder="密码"
              @keyup.enter.native="handleLogin"
              style="width: 100%"
            >
              <img
                slot="prefix"
                src="../assets/images/密码.png"
                class="mr-t16"
                style="width: 24px; height: 24px"
              />
            </el-input>
          </el-form-item>

          <el-checkbox v-model="loginForm.rememberMe" class="mr-t15"
          >记住密码
          </el-checkbox>
          <el-form-item class="mr-t25">
            <el-button
              :loading="loading"
              type="primary"
              class="loginBtn"
              @click="handleLogin"
              style="width: 100%"
            >
              <span v-if="!loading">登录</span>
              <span v-else>登 录 中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="footer">
        <div>
          <div class="d-flex a-center top">
            <div>{{ sysConfig.p1 }}</div>
            <img src="../assets/images/登录页底部装饰.png" alt=""/>

            <div>{{ sysConfig.p2 }}</div>
            <img src="../assets/images/登录页底部装饰.png" alt=""/>

            <div>{{ sysConfig.p3 }}</div>
            <img src="../assets/images/登录页底部装饰.png" alt=""/>

            <div>{{ sysConfig.p4 }}</div>
          </div>
          <img
            src="../assets/images/登录页底部.png"
            style="width: 916px; height: 46px"
            class="mr-t15"
            alt=""
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getSysConfig, getImageBase64, getPublicKey} from "@/api/login";
import Cookies from "js-cookie";
import {encrypt, decrypt} from "@/utils/jsencrypt";
import vueSeamlessScroll from "vue-seamless-scroll";
import axios from "axios";
import moment from "moment";

export default {
  name: "Login",
  components: {
    vueSeamlessScroll,
  },
  data() {
    const checkSilder = (rule, value, callback) => {
      if (!this.$refs.silderVerifyRef.confirmSuccess) {
        return callback(new Error("请完成滑动的拖动"));
      }
      callback();
    };

    return {
      cookiePassword: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
      },
      loginRules: {
        username: [
          {required: true, trigger: "blur", message: "请输入您的账号"},
        ],
        password: [
          {required: true, trigger: "blur", message: "请输入您的密码"},
        ],
        isSilder: [{validator: checkSilder}],
      },
      loading: false,
      // // 验证码开关
      // captchaOnOff: true,
      // 注册开关
      register: false,
      silderVerify: false,
      redirect: undefined,
      sysConfig: {
        subjectImage: "",
        p1: "透明化工厂",
        p2: "精益制造",
        copyright: "",
        p3: "数字化管控",
        p4: "降本增效",
        logo: "",
        title: "数字化修理线IOT平台",
        abbreviation: "LT - IOT",
        url: "",
        docTitle: "",
      },
      visible: false,
      closeFlag: false,
      flag: false,
      type: "1",
      info: {},
      loginErrorCount: 0,
      noticeList: [],
      notice: "",
      pageNum: 1, // 当前页码
      pageSize: 10, // 每页的数量
      total: 0,
      time: "",
      week: "",
      timer: null,
      currentIndex: 0,
      x: 1,
      y: 1,
    };
  },
  computed: {
    defaultOption() {
      return {
        step: 1, // 数值越大速度滚动越快
        limitMoveNum: this.info.info4 ? this.info.info4.length : 20, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
        if (route.query.type) {
          sessionStorage.setItem("loginType", route.query.type);
        }
      },
      immediate: true,
    },
    "sysConfig.docTitle"() {
      document.title = this.sysConfig.docTitle;
      sessionStorage.setItem("docTitle", this.sysConfig.docTitle);
    },
  },
  created() {
    this.getCookie();
    this.getTime();
  },
  methods: {
    resize() {
      this.x = document.body.clientWidth / 1920;
      this.y = document.body.clientHeight / 1080;
    },
    getTime() {
      const timer = setInterval(() => {
        this.time = moment(new Date()).format("yyyy-MM-DD HH:mm:ss");
        this.week = ["日", "一", "二", "三", "四", "五", "六"][
          new Date().getDay()
          ];
        // 定时器操作
      }, 1000);

      // 通过$once来监听定时器，在beforeDestroy钩子可以被清除。
      this.$once("hook:beforeDestroy", () => {
        clearInterval(timer);
      });
    },

    blobToDataURI(blob, callback) {
      var reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = function (e) {
        callback(e.target.result);
      };
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          const type = this.$route.query.type;
          console.log("type-->" + type)
          if (type !== undefined) {
            this.loginForm["type"] = type;
            Cookies.set("type", type, {expires: 30});
          }
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, {expires: 30});
            Cookies.set("password", encrypt(this.loginForm.password), {
              expires: 30,
            });
            Cookies.set("rememberMe", this.loginForm.rememberMe, {
              expires: 30,
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          getPublicKey(1, this.loginForm.username)
            .then((res) => {
              let isLoging = res.data.isLogin;
              let publicKey = res.data.publicKey;
              if (isLoging) {
                this.$confirm("此账号已在线，是否继续登录?", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                })
                  .then(() => {
                    this.loginIng(publicKey);
                  })
                  .catch(() => {
                    this.loading = false;
                  });
              } else {
                this.loginIng(publicKey);
              }
            })
            .catch(() => {
              this.loading = false;
              ++this.loginErrorCount;
              if (this.loginErrorCount >= 2) {
                this.silderVerify = true;
              }
              this.$refs.silderVerifyRef.reset();
            });
        }
      });
    },
    loginIng(publicKey) {
      this.loginForm["publicKey"] = publicKey;
      this.$store
        .dispatch("Login", this.loginForm)

        .then(() => {
          this.$router.push({path: "/index"});
        })
        .catch(() => {
          this.loading = false;
          ++this.loginErrorCount;
          if (this.loginErrorCount >= 2) {
            this.silderVerify = true;
          }
          this.$refs.silderVerifyRef.reset();
        });
    },
    jump() {
      if (this.sysConfig.url) window.open(this.sysConfig.url, "href");
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  background: linear-gradient(180deg, #124B9A, #0D438D);
  position: relative;
  overflow: hidden;

  // 添加动态背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(18, 75, 154, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(13, 67, 141, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 153, 255, 0.1) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
  }

  @keyframes backgroundFloat {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-20px) rotate(1deg);
    }
  }

  .login {
    height: 100%;
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    color: #fefefe;
    z-index: 1;

    .login-box {
      position: absolute;
      right: 220px;
      top: 80px;
      width: 500px;
      height: 566px;
      background: linear-gradient(180deg, rgba(18, 75, 154, 0.9), rgba(13, 67, 141, 0.9));
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      padding: 38px 56px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

      // 添加悬浮动画
      animation: loginBoxFloat 6s ease-in-out infinite;

      @keyframes loginBoxFloat {
        0%, 100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }

      ::v-deep .el-input__inner {
        height: 56px;
        font-size: 16px;
        padding-left: 40px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        color: #ffffff;
        transition: all 0.3s ease;

        &:focus {
          background: rgba(255, 255, 255, 0.15);
          border-color: #0099ff;
          box-shadow: 0 0 15px rgba(0, 153, 255, 0.3);
        }

        &::placeholder {
          color: rgba(255, 255, 255, 0.6);
        }
      }

      ::v-deep .el-checkbox__label {
        font-size: 18px;
        color: #ffffff;
        line-height: 40px;
        background: linear-gradient(135deg, #cef1ff 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      ::v-deep .el-checkbox__inner {
        width: 20px;
        height: 20px;
        transform: translateY(-1px);
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 4px;

        &::after {
          width: 6px;
          height: 12px;
          left: 6px;
          border-color: #0099ff;
        }

        &.is-checked {
          background: linear-gradient(135deg, #0099ff, #0076ff);
          border-color: #0099ff;
        }
      }

      .text {
        font-size: 28px;
        font-weight: bold;
        color: #ffffff;
        line-height: 40px;
        text-align: center;
        background: linear-gradient(135deg, #aed8ff 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .loginBtn {
        height: 54px;
        background: linear-gradient(135deg, #0099ff, #0076ff) !important;
        border: none;
        border-radius: 10px;
        font-size: 20px;
        font-weight: bold;
        outline: none;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 153, 255, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0, 153, 255, 0.4);
          background: linear-gradient(135deg, #00aaff, #0087ff) !important;
        }

        &:active {
          transform: translateY(0px);
        }
      }
    }

    .introduce,
    .cardLogin {
      position: absolute;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: linear-gradient(180deg, rgba(18, 75, 154, 0.8), rgba(13, 67, 141, 0.8));
      backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 15px;
      padding-top: 5px;
      width: 98px;
      height: 98px;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 35px rgba(0, 153, 255, 0.3);
        background: linear-gradient(180deg, rgba(18, 75, 154, 0.9), rgba(13, 67, 141, 0.9));
      }

      & > img {
        width: 48px;
        height: 48px;
        margin-bottom: 5px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
      }

      & > span {
        font-size: 16px;
        color: #ffffff;
        -webkit-text-stroke: 1px #ffffff;
        background: linear-gradient(135deg, #ffffff 0%, #31aaff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }

    .introduce {
      right: 796px;
      top: 551px;
    }

    .cardLogin {
      right: 796px;
      top: 658px;
    }

    .footer {
      position: absolute;
      bottom: 0;
      padding-bottom: 10px;
      display: flex;
      justify-content: center;
      width: 100%;
      z-index: 2;

      .top {
        justify-content: center;
        padding: 20px;
        background: linear-gradient(180deg, rgba(18, 75, 154, 0.6), rgba(13, 67, 141, 0.6));
        backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);

        & > div {
          font-size: 20px;
          line-height: 24px;
          color: #ffffff;
          background: linear-gradient(135deg, #cef1ff 0%, #ffffff 100%);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: 500;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        & > img {
          width: 24px;
          height: 24px;
          margin: 0 15px;
          transform: translateY(3px);
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }
      }
    }
  }
}

.bg {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #124B9A, #0D438D);
  position: relative;
  color: #fff;

  // 添加动态背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(18, 75, 154, 0.4) 0%, transparent 60%),
    radial-gradient(circle at 70% 30%, rgba(13, 67, 141, 0.4) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(0, 153, 255, 0.1) 0%, transparent 70%);
    animation: backgroundPulse 15s ease-in-out infinite;
  }

  @keyframes backgroundPulse {
    0%, 100% {
      opacity: 0.8;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
  }

  .top {
    background: linear-gradient(180deg, rgba(18, 75, 154, 0.9), rgba(13, 67, 141, 0.9));
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    height: 121px;
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 2;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

    .left,
    .right {
      width: 400px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      font-size: 20px;
      height: 60px;
      color: #ffffff;
      line-height: 60px;
      text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
      background: linear-gradient(135deg, #8ad0ff 0%, #ffffff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: 500;
    }

    .center {
      width: 400px;
      text-align: center;
      font-size: 36px;
      font-weight: bold;
      color: #ffffff;
      line-height: 60px;
      text-shadow: 0px 2px 6px rgba(0, 0, 0, 0.4);
      background: linear-gradient(135deg, #8ad0ff 0%, #ffffff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #0099ff, #00aaff);
        border-radius: 2px;
      }
    }

    .divider {
      width: 2px;
      height: 20px;
      background: linear-gradient(180deg, #0099ff, #00aaff);
      border-radius: 1px;
      margin: 0 8px;
      box-shadow: 0 0 10px rgba(0, 153, 255, 0.5);
    }
  }

  .bottom {
    padding: 20px;
    height: calc(100% - 121px);
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 2;

    .left {
      width: 75%;
      display: flex;
      flex-direction: column;

      .notice {
        height: 90px;
        background: linear-gradient(180deg, rgba(18, 75, 154, 0.8), rgba(13, 67, 141, 0.8));
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        padding: 15px;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          width: 4px;
          height: 100%;
          background: linear-gradient(180deg, #0099ff, #00aaff);
          border-radius: 0 2px 2px 0;
        }

        marquee {
          font-size: 32px;
          font-weight: bold;
          color: #8cd4ff;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          flex: 1;
          margin: 0 15px;
        }

        img {
          width: 42px;
          height: 64px;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }
      }

      .info {
        width: 49.5%;
        height: 100%;
        border-radius: 15px;
        font-size: 28px;
        line-height: 35px;
        letter-spacing: 3px;
        background: linear-gradient(180deg, rgba(18, 75, 154, 0.7), rgba(13, 67, 141, 0.7));
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        padding: 20px;
        overflow: auto;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 12px 35px rgba(0, 153, 255, 0.2);
          background: linear-gradient(180deg, rgba(18, 75, 154, 0.8), rgba(13, 67, 141, 0.8));
        }

        // 自定义滚动条
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: linear-gradient(180deg, #0099ff, #00aaff);
          border-radius: 3px;

          &:hover {
            background: linear-gradient(180deg, #00aaff, #0099ff);
          }
        }
      }
    }

    .right {
      width: 24%;
      height: 100%;

      .bz {
        height: calc(100% - 90px);
        background: linear-gradient(180deg, rgba(18, 75, 154, 0.8), rgba(13, 67, 141, 0.8));
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        padding: 20px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 35px rgba(0, 153, 255, 0.2);
        }
      }

      .list {
        overflow: hidden;
        height: calc(100% - 100px);

        img {
          width: 92px;
          height: 92px;
          border-radius: 50%;
          border: 3px solid rgba(255, 255, 255, 0.3);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.05);
            border-color: #0099ff;
            box-shadow: 0 6px 20px rgba(0, 153, 255, 0.4);
          }
        }

        .item {
          font-size: 18px;
          padding: 15px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding-left: 10px;
          }

          .label {
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
          }

          span:not(.label) {
            color: #ffffff;
            font-weight: 600;
          }
        }
      }

      .button {
        height: 70px;
        width: 100%;
        margin-top: 20px;
        background: linear-gradient(135deg, #0099ff, #0076ff);
        border: none;
        border-radius: 15px;
        line-height: 70px;
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        color: #ffffff;
        box-shadow: 0 6px 20px rgba(0, 153, 255, 0.3);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 25px rgba(0, 153, 255, 0.4);
          background: linear-gradient(135deg, #00aaff, #0087ff);

          &::before {
            left: 100%;
          }
        }

        &:active {
          transform: translateY(-1px);
        }
      }
    }

    .tit {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 65px;
      font-size: 22px;
      font-weight: bold;
      color: #d4edff;
      line-height: 30px;
      text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
      margin-bottom: 15px;

      &::after {
        content: '';
        position: absolute;
        bottom: 5px;
        left: 35px;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #0099ff, transparent);
        border-radius: 1px;
      }

      .before {
        width: 35px;
        height: 35px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        margin-right: 10px;
      }

      .zs {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 34px;
        opacity: 0.8;
      }
    }
  }

  .person {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    font-size: 20px;
    margin: 10px 0;
    background: linear-gradient(180deg, rgba(18, 75, 154, 0.6), rgba(13, 67, 141, 0.6));
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(180deg, rgba(18, 75, 154, 0.8), rgba(13, 67, 141, 0.8));
      transform: translateX(5px);
    }
  }
}
</style>
