<template>
  <div class="data-editor">
    <el-form :model="formData" :rules="formRules" ref="dataForm" label-width="120px" size="small">
      <!-- 基本信息 -->
      <div class="form-section">
        <h4>基本信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="数据编码">
              <el-input v-model="formData.dataCode" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="模板名称">
              <el-input v-model="formData.templateName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备类型">
              <el-input v-model="formData.deviceTypeName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 基础数据 -->
      <div class="form-section" v-if="basicFields.length > 0">
        <h4>基础数据</h4>
        <el-row :gutter="20">
          <el-col
            v-for="field in basicFields"
            :key="field.code"
            :span="12"
          >
            <el-form-item
              :label="field.name"
              :prop="`basicData.${field.code}`"
              :rules="getFieldRules(field)"
            >
              <component
                :is="getFieldComponent(field.type)"
                v-model="formData.basicData[field.code]"
                v-bind="getFieldProps(field)"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 表格数据 -->
      <div class="form-section" v-if="tableFields.length > 0">
        <div class="section-header">
          <h4>表格数据</h4>
          <div class="section-actions">
            <el-button type="primary" size="mini" @click="addTableRow">
              <i class="el-icon-plus"></i>
              添加行
            </el-button>
          </div>
        </div>

        <el-table :data="formData.tableData" border size="small">
          <el-table-column
            v-for="field in tableFields"
            :key="field.code"
            :label="field.name"
            :width="field.width"
          >
            <template slot-scope="scope">
              <component
                :is="getFieldComponent(field.type)"
                v-model="scope.row[field.code]"
                v-bind="getFieldProps(field)"
                size="mini"
                @change="validateTableField(scope.$index, field.code)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                icon="el-icon-delete"
                @click="removeTableRow(scope.$index)"
                style="color: #f56c6c"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="table-empty" v-if="formData.tableData.length === 0">
          <p>暂无数据，点击"添加行"按钮添加数据</p>
        </div>
      </div>
    </el-form>

    <div class="editor-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataEditor',
  props: {
    data: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      saving: false,
      formData: {
        dataCode: '',
        templateName: '',
        deviceTypeName: '',
        basicData: {},
        tableData: []
      },
      formRules: {},
      tableFieldErrors: {}
    }
  },
  computed: {
    basicFields() {
      return this.data.templateConfig?.basicFields || []
    },
    tableFields() {
      return this.data.templateConfig?.tableFields || []
    }
  },
  watch: {
    data: {
      handler() {
        this.initFormData()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    initFormData() {
      this.formData = {
        dataCode: this.data.dataCode || '',
        templateName: this.data.templateName || '',
        deviceTypeName: this.data.deviceTypeName || '',
        basicData: { ...this.data.basicData } || {},
        tableData: JSON.parse(JSON.stringify(this.data.tableData || []))
      }

      // 初始化基础数据字段
      this.basicFields.forEach(field => {
        if (!(field.code in this.formData.basicData)) {
          this.$set(this.formData.basicData, field.code, field.defaultValue || '')
        }
      })

      // 确保表格数据至少有一行
      if (this.formData.tableData.length === 0 && this.tableFields.length > 0) {
        this.addTableRow()
      }

      this.generateFormRules()
    },

    generateFormRules() {
      const rules = {}
      
      // 基础字段验证规则
      this.basicFields.forEach(field => {
        if (field.required) {
          rules[`basicData.${field.code}`] = [
            { required: true, message: `${field.name}不能为空`, trigger: 'blur' }
          ]
        }
      })

      this.formRules = rules
    },

    getFieldRules(field) {
      const rules = []
      
      if (field.required) {
        rules.push({ required: true, message: `${field.name}不能为空`, trigger: 'blur' })
      }

      if (field.type === 'number') {
        rules.push({ type: 'number', message: `${field.name}必须为数字`, trigger: 'blur' })
        if (field.min !== undefined) {
          rules.push({ type: 'number', min: field.min, message: `${field.name}不能小于${field.min}`, trigger: 'blur' })
        }
        if (field.max !== undefined) {
          rules.push({ type: 'number', max: field.max, message: `${field.name}不能大于${field.max}`, trigger: 'blur' })
        }
      }

      if (field.type === 'text' || field.type === 'textarea') {
        if (field.maxLength) {
          rules.push({ max: field.maxLength, message: `${field.name}长度不能超过${field.maxLength}个字符`, trigger: 'blur' })
        }
      }

      return rules
    },

    getFieldComponent(type) {
      const componentMap = {
        text: 'el-input',
        textarea: 'el-input',
        number: 'el-input-number',
        date: 'el-date-picker',
        datetime: 'el-date-picker',
        select: 'el-select',
        radio: 'el-radio-group',
        checkbox: 'el-checkbox-group'
      }
      return componentMap[type] || 'el-input'
    },

    getFieldProps(field) {
      const props = {
        placeholder: field.placeholder || `请输入${field.name}`,
        clearable: true
      }

      if (field.type === 'textarea') {
        props.type = 'textarea'
        props.rows = field.rows || 3
      } else if (field.type === 'number') {
        props.min = field.min
        props.max = field.max
        props.step = field.step || 1
        props.precision = field.precision || 0
        props.controlsPosition = 'right'
      } else if (field.type === 'date') {
        props.type = 'date'
        props.format = field.format || 'yyyy-MM-dd'
        props.valueFormat = 'yyyy-MM-dd'
      } else if (field.type === 'datetime') {
        props.type = 'datetime'
        props.format = field.format || 'yyyy-MM-dd HH:mm:ss'
        props.valueFormat = 'yyyy-MM-dd HH:mm:ss'
      } else if (field.type === 'select') {
        props.filterable = field.filterable || false
      }

      return props
    },

    addTableRow() {
      const newRow = {}
      this.tableFields.forEach(field => {
        newRow[field.code] = field.defaultValue || ''
      })
      this.formData.tableData.push(newRow)
    },

    removeTableRow(index) {
      this.formData.tableData.splice(index, 1)
    },

    validateTableField(rowIndex, fieldCode) {
      const field = this.tableFields.find(f => f.code === fieldCode)
      const value = this.formData.tableData[rowIndex][fieldCode]
      
      // 这里可以添加表格字段的验证逻辑
      if (field.required && (!value || value === '')) {
        this.$set(this.tableFieldErrors, `${rowIndex}_${fieldCode}`, `${field.name}不能为空`)
      } else {
        this.$delete(this.tableFieldErrors, `${rowIndex}_${fieldCode}`)
      }
    },

    handleSave() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          // 验证表格数据
          let tableValid = true
          this.formData.tableData.forEach((row, index) => {
            this.tableFields.forEach(field => {
              this.validateTableField(index, field.code)
            })
          })

          if (Object.keys(this.tableFieldErrors).length > 0) {
            tableValid = false
            this.$message.error('表格数据存在错误，请检查后重试')
          }

          if (tableValid) {
            this.saving = true
            const saveData = {
              basicData: this.formData.basicData,
              tableData: this.formData.tableData
            }
            
            // 模拟保存延迟
            setTimeout(() => {
              this.saving = false
              this.$emit('save', saveData)
            }, 1000)
          }
        }
      })
    },

    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.data-editor {
  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 4px;

    h4 {
      margin: 0 0 20px 0;
      color: #333;
      font-size: 16px;
      border-bottom: 1px solid #e6e6e6;
      padding-bottom: 10px;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h4 {
        margin: 0;
        border: none;
        padding: 0;
      }
    }

    .table-empty {
      text-align: center;
      padding: 40px;
      color: #999;
      background: #fff;
      border: 1px dashed #e6e6e6;
      border-radius: 4px;
    }
  }

  .editor-footer {
    margin-top: 30px;
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e6e6e6;

    .el-button {
      margin: 0 10px;
    }
  }

  ::v-deep .el-select {
    width: 100%;
  }

  ::v-deep .el-date-editor {
    width: 100%;
  }
}
</style>
