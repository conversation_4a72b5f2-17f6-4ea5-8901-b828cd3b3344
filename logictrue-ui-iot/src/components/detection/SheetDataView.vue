<template>
  <div class="sheet-data-view">
    <!-- 基础字段 -->
    <div v-if="sheetData.basicFields && sheetData.basicFields.length > 0" class="basic-fields-section">
      <h4>基础字段</h4>
      <el-row :gutter="20">
        <el-col 
          :span="8" 
          v-for="field in sheetData.basicFields" 
          :key="field.fieldCode"
          class="mb15"
        >
          <div class="field-item">
            <div class="field-label">{{ field.fieldName }}</div>
            <div class="field-value">{{ field.fieldValue || '-' }}</div>
            <div class="field-meta">
              <span class="field-position">{{ field.valuePosition }}</span>
              <span class="field-type">{{ getFieldTypeText(field.fieldType) }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 表格数据 -->
    <div v-if="sheetData.tableHeaders && sheetData.tableHeaders.length > 0" class="table-data-section">
      <h4>表格数据</h4>
      
      <!-- 表格 -->
      <el-table 
        :data="sheetData.tableDataList" 
        border 
        stripe
        style="width: 100%"
        max-height="400"
      >
        <el-table-column 
          type="index" 
          label="序号" 
          width="60" 
          align="center"
        />
        <el-table-column
          v-for="header in sortedHeaders"
          :key="header.headerCode"
          :prop="header.headerCode"
          :label="header.headerName"
          :width="getColumnWidth(header)"
          show-overflow-tooltip
          align="center"
        >
          <template slot="header" slot-scope="scope">
            <div class="table-header">
              <div class="header-name">{{ header.headerName }}</div>
              <div class="header-position">{{ header.headerPosition }}</div>
            </div>
          </template>
          <template slot-scope="scope">
            <span :class="getValueClass(scope.row[header.headerCode], header.dataType)">
              {{ formatValue(scope.row[header.headerCode], header.dataType) }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 表格统计信息 -->
      <div class="table-summary" v-if="sheetData.tableDataList && sheetData.tableDataList.length > 0">
        <span>共 {{ sheetData.tableDataList.length }} 行数据</span>
        <span>{{ sheetData.tableHeaders.length }} 列</span>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-if="!hasData" class="no-data">
      <i class="el-icon-document"></i>
      <p>该Sheet暂无解析数据</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SheetDataView',
  props: {
    sheetData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    /** 是否有数据 */
    hasData() {
      const hasBasicFields = this.sheetData.basicFields && this.sheetData.basicFields.length > 0
      const hasTableData = this.sheetData.tableHeaders && this.sheetData.tableHeaders.length > 0
      return hasBasicFields || hasTableData
    },
    /** 排序后的表头 */
    sortedHeaders() {
      if (!this.sheetData.tableHeaders) return []
      return [...this.sheetData.tableHeaders].sort((a, b) => (a.columnOrder || 0) - (b.columnOrder || 0))
    }
  },
  methods: {
    /** 获取字段类型文本 */
    getFieldTypeText(type) {
      const typeMap = {
        'text': '文本',
        'number': '数字',
        'date': '日期',
        'datetime': '日期时间'
      }
      return typeMap[type] || '文本'
    },
    /** 获取列宽度 */
    getColumnWidth(header) {
      // 根据表头名称长度和数据类型动态计算宽度
      const nameLength = header.headerName ? header.headerName.length : 0
      let width = Math.max(nameLength * 15, 100)
      
      if (header.dataType === 'date' || header.dataType === 'datetime') {
        width = Math.max(width, 150)
      }
      
      return Math.min(width, 200)
    },
    /** 格式化值 */
    formatValue(value, dataType) {
      if (value === null || value === undefined || value === '') {
        return '-'
      }
      
      switch (dataType) {
        case 'number':
          return isNaN(value) ? value : Number(value).toLocaleString()
        case 'date':
          return this.formatDate(value)
        case 'datetime':
          return this.formatDateTime(value)
        default:
          return value
      }
    },
    /** 格式化日期 */
    formatDate(value) {
      try {
        const date = new Date(value)
        if (isNaN(date.getTime())) return value
        return date.toLocaleDateString('zh-CN')
      } catch (e) {
        return value
      }
    },
    /** 格式化日期时间 */
    formatDateTime(value) {
      try {
        const date = new Date(value)
        if (isNaN(date.getTime())) return value
        return date.toLocaleString('zh-CN')
      } catch (e) {
        return value
      }
    },
    /** 获取值的样式类 */
    getValueClass(value, dataType) {
      if (value === null || value === undefined || value === '') {
        return 'empty-value'
      }
      
      switch (dataType) {
        case 'number':
          return 'number-value'
        case 'date':
        case 'datetime':
          return 'date-value'
        default:
          return 'text-value'
      }
    }
  }
}
</script>

<style scoped>
.sheet-data-view {
  padding: 10px 0;
  background: transparent;
}

.basic-fields-section {
  margin-bottom: 30px;
}

.basic-fields-section h4 {
  margin: 0 0 15px 0;
  color: #ffffff;
  font-size: 16px;
  border-left: 4px solid #ffffff;
  padding-left: 10px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.field-item {
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 18px;
  background: linear-gradient(180deg, #124B9A, #0D438D);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.field-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.field-label {
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10px;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  opacity: 0.9;
}

.field-value {
  color: #ffffff;
  font-size: 18px;
  margin-bottom: 12px;
  min-height: 22px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.field-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.field-position {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4px);
}

.field-type {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
}

.table-data-section h4 {
  margin: 0 0 15px 0;
  color: #ffffff;
  font-size: 16px;
  border-left: 4px solid #ffffff;
  padding-left: 10px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.table-header {
  text-align: center;
}

.header-name {
  font-weight: bold;
  margin-bottom: 2px;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.header-position {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.15);
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.table-summary {
  margin-top: 10px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
}

.table-summary span {
  margin-right: 20px;
  font-weight: 500;
}

.empty-value {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.number-value {
  color: #FFD700;
  font-weight: 600;
}

.date-value {
  color: #87CEEB;
  font-weight: 500;
}

.text-value {
  color: #ffffff;
  font-weight: 500;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: rgba(255, 255, 255, 0.6);
}

.no-data i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
  color: rgba(255, 255, 255, 0.4);
}

.mb15 {
  margin-bottom: 15px;
}
</style>
