<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="field-edit-dialog">
      <el-form :model="fieldData" :rules="fieldRules" ref="fieldForm" label-width="100px" size="small">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="字段名称" prop="name">
              <el-input v-model="fieldData.name" placeholder="请输入字段名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字段编码" prop="code">
              <el-input v-model="fieldData.code" placeholder="请输入字段编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="字段类型" prop="type">
              <el-select v-model="fieldData.type" placeholder="请选择字段类型" @change="handleTypeChange" style="width: 100%">
                <el-option
                  v-for="option in fieldTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否必填">
              <el-switch v-model="fieldData.required" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 文本类型配置 -->
        <template v-if="fieldData.type === 'text' || fieldData.type === 'textarea'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="占位符">
                <el-input v-model="fieldData.placeholder" placeholder="请输入占位符" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="默认值">
                <el-input v-model="fieldData.defaultValue" placeholder="请输入默认值" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="fieldData.type === 'text'">
            <el-col :span="12">
              <el-form-item label="最大长度">
                <el-input-number v-model="fieldData.maxLength" :min="1" :max="1000" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="fieldData.type === 'textarea'">
            <el-col :span="12">
              <el-form-item label="行数">
                <el-input-number v-model="fieldData.rows" :min="2" :max="10" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <!-- 数字类型配置 -->
        <template v-if="fieldData.type === 'number'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="最小值">
                <el-input-number v-model="fieldData.min" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大值">
                <el-input-number v-model="fieldData.max" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="步长">
                <el-input-number v-model="fieldData.step" :min="0.01" :step="0.01" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="精度">
                <el-input-number v-model="fieldData.precision" :min="0" :max="10" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <!-- 日期类型配置 -->
        <template v-if="fieldData.type === 'date' || fieldData.type === 'datetime'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="日期格式">
                <el-select v-model="fieldData.format" style="width: 100%">
                  <el-option
                    v-for="format in dateFormats"
                    :key="format.value"
                    :label="format.label"
                    :value="format.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="默认值">
                <el-select v-model="fieldData.defaultValue" style="width: 100%">
                  <el-option label="无" value="" />
                  <el-option label="当前日期" value="today" />
                  <el-option label="当前时间" value="now" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <!-- 选择类型配置 -->
        <template v-if="fieldData.type === 'select' || fieldData.type === 'radio' || fieldData.type === 'checkbox'">
          <el-form-item label="选项配置">
            <div class="options-config">
              <div
                v-for="(option, index) in fieldData.options"
                :key="index"
                class="option-item"
              >
                <el-input
                  v-model="fieldData.options[index]"
                  placeholder="请输入选项值"
                  size="small"
                />
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="removeOption(index)"
                  size="small"
                />
              </div>
              <el-button
                type="text"
                icon="el-icon-plus"
                @click="addOption"
                size="small"
              >
                添加选项
              </el-button>
            </div>
          </el-form-item>

          <el-row :gutter="20" v-if="fieldData.type === 'select'">
            <el-col :span="12">
              <el-form-item label="允许清空">
                <el-switch v-model="fieldData.clearable" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="可搜索">
                <el-switch v-model="fieldData.filterable" />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <!-- 表格字段特有配置 -->
        <template v-if="fieldType === 'table'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="列宽">
                <el-input-number v-model="fieldData.width" :min="80" :max="500" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="固定列">
                <el-select v-model="fieldData.fixed" style="width: 100%">
                  <el-option label="不固定" value="" />
                  <el-option label="左侧固定" value="left" />
                  <el-option label="右侧固定" value="right" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="排序">
                <el-switch v-model="fieldData.sortable" />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序">
              <el-input-number v-model="fieldData.order" :min="1" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'FieldEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    field: {
      type: Object,
      default: () => ({})
    },
    fieldType: {
      type: String,
      default: 'basic' // 'basic' or 'table'
    }
  },
  data() {
    return {
      dialogVisible: false,
      fieldData: {},
      fieldTypeOptions: [
        { label: '单行文本', value: 'text' },
        { label: '多行文本', value: 'textarea' },
        { label: '数字', value: 'number' },
        { label: '日期', value: 'date' },
        { label: '日期时间', value: 'datetime' },
        { label: '下拉选择', value: 'select' },
        { label: '单选', value: 'radio' },
        { label: '多选', value: 'checkbox' }
      ],
      dateFormats: [
        { label: 'YYYY-MM-DD', value: 'yyyy-MM-dd' },
        { label: 'YYYY/MM/DD', value: 'yyyy/MM/dd' },
        { label: 'YYYY-MM-DD HH:mm:ss', value: 'yyyy-MM-dd HH:mm:ss' },
        { label: 'YYYY/MM/DD HH:mm:ss', value: 'yyyy/MM/dd HH:mm:ss' }
      ],
      fieldRules: {
        name: [
          { required: true, message: '请输入字段名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入字段编码', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择字段类型', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.fieldData.code ? '编辑字段' : '新增字段'
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    field: {
      handler(val) {
        this.initFieldData(val)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    initFieldData(field) {
      this.fieldData = {
        code: '',
        name: '',
        type: 'text',
        required: false,
        placeholder: '',
        defaultValue: '',
        options: [],
        rows: 3,
        maxLength: 100,
        min: undefined,
        max: undefined,
        step: 1,
        precision: 0,
        format: 'yyyy-MM-dd',
        clearable: true,
        filterable: false,
        width: 150,
        fixed: '',
        sortable: false,
        order: 1,
        ...field
      }
    },

    handleTypeChange() {
      // 重置类型相关的属性
      if (this.fieldData.type === 'select' || this.fieldData.type === 'radio' || this.fieldData.type === 'checkbox') {
        if (!this.fieldData.options || this.fieldData.options.length === 0) {
          this.fieldData.options = ['选项1', '选项2']
        }
      }
    },

    addOption() {
      if (!this.fieldData.options) {
        this.fieldData.options = []
      }
      this.fieldData.options.push(`选项${this.fieldData.options.length + 1}`)
    },

    removeOption(index) {
      this.fieldData.options.splice(index, 1)
    },

    handleConfirm() {
      this.$refs.fieldForm.validate((valid) => {
        if (valid) {
          this.$emit('confirm', { ...this.fieldData })
          this.handleClose()
        }
      })
    },

    handleCancel() {
      this.handleClose()
    },

    handleClose() {
      this.dialogVisible = false
      this.$refs.fieldForm && this.$refs.fieldForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.field-edit-dialog {
  .options-config {
    .option-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .el-input {
        flex: 1;
        margin-right: 10px;
      }
    }
  }
}
</style>
