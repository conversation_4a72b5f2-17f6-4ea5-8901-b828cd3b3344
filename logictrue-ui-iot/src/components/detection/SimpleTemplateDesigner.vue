<template>
  <div class="simple-template-designer">
    <div class="designer-header">
      <el-form :model="templateInfo" label-width="100px" size="small">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板名称">
              <el-input v-model="templateInfo.name" placeholder="请输入模板名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板编码">
              <el-input v-model="templateInfo.code" placeholder="请输入模板编码"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="designer-body">
      <el-row :gutter="20">
        <!-- 字段库 -->
        <el-col :span="6">
          <div class="field-library">
            <div class="library-header">
              <h4>字段库</h4>
            </div>
            <div class="library-content">
              <h5>基础字段</h5>
              <div class="field-list">
                <div
                  v-for="field in basicFieldLibrary"
                  :key="field.type"
                  class="field-item"
                  draggable="true"
                  @dragstart="handleDragStart($event, field, 'basic')"
                >
                  <i :class="field.icon"></i>
                  <span>{{ field.name }}</span>
                </div>
              </div>
              
              <h5>表格字段</h5>
              <div class="field-list">
                <div
                  v-for="field in tableFieldLibrary"
                  :key="field.type"
                  class="field-item"
                  draggable="true"
                  @dragstart="handleDragStart($event, field, 'table')"
                >
                  <i :class="field.icon"></i>
                  <span>{{ field.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 设计区域 -->
        <el-col :span="18">
          <div class="design-area">
            <el-tabs v-model="activeTab" type="card">
              <el-tab-pane label="基础数据配置" name="basic">
                <div class="basic-fields-container">
                  <div class="container-header">
                    <span>基础数据字段</span>
                    <el-button type="text" size="mini" @click="clearBasicFields">清空</el-button>
                  </div>
                  <div
                    class="drop-zone"
                    :class="{ 'drag-over': dragOverBasic }"
                    @drop="handleDrop($event, 'basic')"
                    @dragover.prevent="handleDragOver($event, 'basic')"
                    @dragenter.prevent="handleDragEnter($event, 'basic')"
                    @dragleave="handleDragLeave($event, 'basic')"
                  >
                    <div v-if="templateConfig.basicFields.length === 0" class="empty-drop-zone">
                      <i class="el-icon-plus"></i>
                      <p>拖拽字段到此处配置基础数据</p>
                    </div>
                    <div
                      v-for="(field, index) in templateConfig.basicFields"
                      :key="field.code"
                      class="field-config-item"
                      @click="selectField(field, 'basic', index)"
                    >
                      <div class="field-header">
                        <span class="field-label">{{ field.name }}</span>
                        <div class="field-actions">
                          <el-button type="text" size="mini" @click.stop="removeField('basic', index)">
                            <i class="el-icon-delete"></i>
                          </el-button>
                        </div>
                      </div>
                      <div class="field-info">
                        <span class="field-type">{{ field.type }}</span>
                        <span class="field-required">{{ field.required ? '必填' : '可选' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="表格数据配置" name="table">
                <div class="table-fields-container">
                  <div class="container-header">
                    <span>表格字段</span>
                    <el-button type="text" size="mini" @click="clearTableFields">清空</el-button>
                  </div>
                  <div
                    class="drop-zone"
                    :class="{ 'drag-over': dragOverTable }"
                    @drop="handleDrop($event, 'table')"
                    @dragover.prevent="handleDragOver($event, 'table')"
                    @dragenter.prevent="handleDragEnter($event, 'table')"
                    @dragleave="handleDragLeave($event, 'table')"
                  >
                    <div v-if="templateConfig.tableFields.length === 0" class="empty-drop-zone">
                      <i class="el-icon-plus"></i>
                      <p>拖拽字段到此处配置表格列</p>
                    </div>
                    <div
                      v-for="(field, index) in templateConfig.tableFields"
                      :key="field.code"
                      class="field-config-item"
                      @click="selectField(field, 'table', index)"
                    >
                      <div class="field-header">
                        <span class="field-label">{{ field.name }}</span>
                        <div class="field-actions">
                          <el-button type="text" size="mini" @click.stop="removeField('table', index)">
                            <i class="el-icon-delete"></i>
                          </el-button>
                        </div>
                      </div>
                      <div class="field-info">
                        <span class="field-type">{{ field.type }}</span>
                        <span class="field-width">宽度: {{ field.width || 150 }}px</span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="designer-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">保存模板</el-button>
    </div>

    <!-- 调试信息 -->
    <div class="debug-info">
      <h4>调试信息</h4>
      <el-tabs>
        <el-tab-pane label="基础字段" name="basic-debug">
          <pre>{{ JSON.stringify(templateConfig.basicFields, null, 2) }}</pre>
        </el-tab-pane>
        <el-tab-pane label="表格字段" name="table-debug">
          <pre>{{ JSON.stringify(templateConfig.tableFields, null, 2) }}</pre>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleTemplateDesigner',
  data() {
    return {
      dragOverBasic: false,
      dragOverTable: false,
      activeTab: 'basic',
      selectedField: null,
      selectedFieldType: null,
      selectedFieldIndex: -1,
      templateInfo: {
        name: '测试模板',
        code: 'TEST_TEMPLATE_001'
      },
      templateConfig: {
        version: '1.0',
        basicFields: [],
        tableFields: []
      },
      basicFieldLibrary: [
        { type: 'text', name: '单行文本', icon: 'el-icon-edit-outline' },
        { type: 'textarea', name: '多行文本', icon: 'el-icon-document' },
        { type: 'number', name: '数字', icon: 'el-icon-s-data' },
        { type: 'date', name: '日期', icon: 'el-icon-date' },
        { type: 'select', name: '下拉选择', icon: 'el-icon-arrow-down' }
      ],
      tableFieldLibrary: [
        { type: 'text', name: '文本列', icon: 'el-icon-edit-outline' },
        { type: 'number', name: '数字列', icon: 'el-icon-s-data' },
        { type: 'select', name: '选择列', icon: 'el-icon-arrow-down' },
        { type: 'date', name: '日期列', icon: 'el-icon-date' }
      ]
    }
  },
  methods: {
    // 拖拽开始
    handleDragStart(event, field, category) {
      console.log('拖拽开始:', field, category)
      event.dataTransfer.effectAllowed = 'copy'
      const dragData = { ...field, category }
      event.dataTransfer.setData('text/plain', JSON.stringify(dragData))
      event.dataTransfer.setData('field', JSON.stringify(dragData))
    },

    // 拖拽进入
    handleDragEnter(event, targetCategory) {
      event.preventDefault()
      if (targetCategory === 'basic') {
        this.dragOverBasic = true
      } else {
        this.dragOverTable = true
      }
      console.log('拖拽进入:', targetCategory)
    },

    // 拖拽悬停
    handleDragOver(event, targetCategory) {
      event.preventDefault()
      event.dataTransfer.dropEffect = 'copy'
    },

    // 拖拽离开
    handleDragLeave(event, targetCategory) {
      const rect = event.currentTarget.getBoundingClientRect()
      const x = event.clientX
      const y = event.clientY
      
      if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
        if (targetCategory === 'basic') {
          this.dragOverBasic = false
        } else {
          this.dragOverTable = false
        }
        console.log('拖拽离开:', targetCategory)
      }
    },

    // 拖拽放置
    handleDrop(event, targetCategory) {
      event.preventDefault()
      event.stopPropagation()
      console.log('拖拽放置:', targetCategory)
      
      // 重置拖拽状态
      this.dragOverBasic = false
      this.dragOverTable = false
      
      try {
        const fieldData = JSON.parse(event.dataTransfer.getData('field') || event.dataTransfer.getData('text/plain'))
        console.log('解析的数据:', fieldData)
        
        if (!fieldData || !fieldData.type) {
          console.error('拖拽数据无效:', fieldData)
          this.$message.error('拖拽数据无效')
          return
        }
        
        if (fieldData.category !== targetCategory) {
          this.$message.warning('字段类型不匹配')
          return
        }

        this.addField(fieldData, targetCategory)
      } catch (error) {
        console.error('解析拖拽数据失败:', error)
        this.$message.error('拖拽数据格式错误')
      }
    },

    // 添加字段
    addField(fieldTemplate, category) {
      console.log('添加字段:', fieldTemplate, category)
      
      const field = {
        code: `${fieldTemplate.type}_${Date.now()}`,
        name: fieldTemplate.name,
        type: fieldTemplate.type,
        required: false,
        defaultValue: '',
        placeholder: `请输入${fieldTemplate.name}`,
        order: category === 'basic' ? this.templateConfig.basicFields.length + 1 : this.templateConfig.tableFields.length + 1
      }

      if (category === 'table') {
        field.width = 150
      }

      if (fieldTemplate.type === 'select') {
        field.options = ['选项1', '选项2']
      }

      if (category === 'basic') {
        this.templateConfig.basicFields.push(field)
        console.log('添加基础字段成功:', field)
      } else {
        this.templateConfig.tableFields.push(field)
        console.log('添加表格字段成功:', field)
      }
      
      this.$message.success(`添加字段: ${field.name}`)
    },

    // 选择字段
    selectField(field, type, index) {
      this.selectedField = { ...field }
      this.selectedFieldType = type
      this.selectedFieldIndex = index
      console.log('选择字段:', field, type, index)
    },

    // 删除字段
    removeField(type, index) {
      if (type === 'basic') {
        this.templateConfig.basicFields.splice(index, 1)
      } else {
        this.templateConfig.tableFields.splice(index, 1)
      }
      console.log('删除字段:', type, index)
    },

    // 清空基础字段
    clearBasicFields() {
      this.templateConfig.basicFields = []
      console.log('清空基础字段')
    },

    // 清空表格字段
    clearTableFields() {
      this.templateConfig.tableFields = []
      console.log('清空表格字段')
    },

    // 保存
    handleSave() {
      const templateData = {
        templateName: this.templateInfo.name,
        templateCode: this.templateInfo.code,
        templateConfig: this.templateConfig
      }
      console.log('保存模板:', templateData)
      this.$message.success('模板保存成功')
      this.$emit('save', templateData)
    },

    // 取消
    handleCancel() {
      console.log('取消操作')
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.simple-template-designer {
  height: 100%;
  display: flex;
  flex-direction: column;

  .designer-header {
    padding: 20px;
    background: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;
  }

  .designer-body {
    flex: 1;
    padding: 20px;
    overflow: hidden;

    .field-library {
      height: 100%;
      border: 1px solid #e6e6e6;
      border-radius: 4px;

      .library-header {
        padding: 10px 15px;
        background: #fafafa;
        border-bottom: 1px solid #e6e6e6;

        h4 {
          margin: 0;
          font-size: 14px;
          color: #333;
        }
      }

      .library-content {
        height: calc(100% - 45px);
        overflow-y: auto;
        padding: 10px;

        h5 {
          margin: 15px 0 10px 0;
          font-size: 13px;
          color: #666;
          border-bottom: 1px solid #f0f0f0;
          padding-bottom: 5px;

          &:first-child {
            margin-top: 0;
          }
        }

        .field-list {
          .field-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            cursor: grab;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
            }

            &:active {
              cursor: grabbing;
            }

            i {
              margin-right: 8px;
              color: #409eff;
            }

            span {
              font-size: 13px;
              color: #333;
            }
          }
        }
      }
    }

    .design-area {
      height: 100%;
      border: 1px solid #e6e6e6;
      border-radius: 4px;

      .el-tabs {
        height: 100%;

        ::v-deep .el-tabs__content {
          height: calc(100% - 55px);
          overflow-y: auto;
        }
      }

      .basic-fields-container,
      .table-fields-container {
        height: 100%;
        padding: 15px;

        .container-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
          padding-bottom: 10px;
          border-bottom: 1px solid #e6e6e6;

          span {
            font-weight: 500;
            color: #333;
          }
        }

        .drop-zone {
          height: calc(100% - 50px);
          min-height: 300px;
          border: 2px dashed #d9d9d9;
          border-radius: 4px;
          position: relative;
          overflow-y: auto;
          padding: 10px;

          &:hover {
            border-color: #409eff;
          }

          &.drag-over {
            border-color: #67c23a;
            background-color: #f0f9ff;
          }

          .empty-drop-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #999;

            i {
              font-size: 48px;
              margin-bottom: 10px;
              display: block;
            }

            p {
              margin: 0;
              font-size: 14px;
            }
          }

          .field-config-item {
            margin-bottom: 10px;
            padding: 15px;
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
            }

            .field-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .field-label {
                font-weight: 500;
                color: #333;
              }

              .field-actions {
                opacity: 0;
                transition: opacity 0.3s;

                .el-button {
                  padding: 0;
                  margin-left: 5px;
                }
              }
            }

            &:hover .field-actions {
              opacity: 1;
            }

            .field-info {
              display: flex;
              gap: 15px;
              font-size: 12px;
              color: #666;

              .field-type {
                padding: 2px 6px;
                background: #e6f7ff;
                border-radius: 2px;
                color: #1890ff;
              }

              .field-required {
                padding: 2px 6px;
                background: #fff2e8;
                border-radius: 2px;
                color: #fa8c16;
              }

              .field-width {
                padding: 2px 6px;
                background: #f6ffed;
                border-radius: 2px;
                color: #52c41a;
              }
            }
          }
        }
      }
    }
  }

  .designer-footer {
    padding: 15px 20px;
    background: #fafafa;
    border-top: 1px solid #e6e6e6;
    text-align: right;

    .el-button {
      margin-left: 10px;
    }
  }

  .debug-info {
    margin-top: 20px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 4px;

    h4 {
      margin: 0 0 15px 0;
      color: #333;
    }

    pre {
      background: #fff;
      padding: 15px;
      border-radius: 4px;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #e6e6e6;
    }
  }
}
</style>
