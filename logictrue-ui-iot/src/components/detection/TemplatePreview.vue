<template>
  <div class="template-preview">
    <div class="preview-header">
      <h3>{{ template.templateName || '模板预览' }}</h3>
      <div class="template-info">
        <el-descriptions :column="2" size="small">
          <el-descriptions-item label="模板编码">{{ template.templateCode }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">{{ template.deviceTypeName }}</el-descriptions-item>
          <el-descriptions-item label="版本">{{ template.version || '1.0' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ template.createTime }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="preview-content">
      <el-tabs v-model="activeTab" type="card">
        <!-- 基础数据预览 -->
        <el-tab-pane label="基础数据" name="basic">
          <div class="basic-preview">
            <h4>基础数据字段</h4>
            <el-form
              :model="basicData"
              label-width="120px"
              size="small"
              class="preview-form"
            >
              <el-row :gutter="20">
                <el-col
                  v-for="field in basicFields"
                  :key="field.code"
                  :span="12"
                >
                  <el-form-item :label="field.name" :required="field.required">
                    <component
                      :is="getFieldComponent(field.type)"
                      v-model="basicData[field.code]"
                      v-bind="getFieldProps(field)"
                      :disabled="true"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 表格数据预览 -->
        <el-tab-pane label="表格数据" name="table">
          <div class="table-preview">
            <h4>表格字段</h4>
            <el-table
              :data="tableData"
              border
              size="small"
              class="preview-table"
            >
              <el-table-column
                v-for="field in tableFields"
                :key="field.code"
                :prop="field.code"
                :label="field.name"
                :width="field.width"
                :fixed="field.fixed"
                :sortable="field.sortable"
              >
                <template slot-scope="scope">
                  <component
                    :is="getFieldComponent(field.type)"
                    v-model="scope.row[field.code]"
                    v-bind="getFieldProps(field)"
                    :disabled="true"
                    size="mini"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- Excel配置预览 -->
        <el-tab-pane label="Excel配置" name="excel">
          <div class="excel-preview">
            <h4>Excel导出配置</h4>
            <el-descriptions :column="2" size="small" border>
              <el-descriptions-item label="工作表名称">
                {{ excelConfig.sheetName || '检测数据' }}
              </el-descriptions-item>
              <el-descriptions-item label="起始行">
                {{ excelConfig.startRow || 1 }}
              </el-descriptions-item>
              <el-descriptions-item label="基础数据起始行">
                {{ excelConfig.basicDataStartRow || 1 }}
              </el-descriptions-item>
              <el-descriptions-item label="表格数据起始行">
                {{ excelConfig.tableDataStartRow || 5 }}
              </el-descriptions-item>
            </el-descriptions>

            <div class="excel-layout-preview">
              <h5>Excel布局预览</h5>
              <div class="excel-mock">
                <!-- 基础数据区域 -->
                <div class="excel-section">
                  <div class="section-title">基础数据</div>
                  <div class="excel-rows">
                    <div
                      v-for="field in basicFields"
                      :key="field.code"
                      class="excel-row"
                    >
                      <div class="excel-cell label-cell">{{ field.name }}</div>
                      <div class="excel-cell value-cell">{{ getFieldPlaceholder(field) }}</div>
                    </div>
                  </div>
                </div>

                <!-- 表格数据区域 -->
                <div class="excel-section">
                  <div class="section-title">表格数据</div>
                  <div class="excel-table">
                    <div class="excel-header">
                      <div
                        v-for="field in tableFields"
                        :key="field.code"
                        class="excel-cell header-cell"
                        :style="{ width: field.width + 'px' }"
                      >
                        {{ field.name }}
                      </div>
                    </div>
                    <div class="excel-row">
                      <div
                        v-for="field in tableFields"
                        :key="field.code"
                        class="excel-cell data-cell"
                        :style="{ width: field.width + 'px' }"
                      >
                        {{ getFieldPlaceholder(field) }}
                      </div>
                    </div>
                    <div class="excel-row">
                      <div
                        v-for="field in tableFields"
                        :key="field.code"
                        class="excel-cell data-cell"
                        :style="{ width: field.width + 'px' }"
                      >
                        {{ getFieldPlaceholder(field) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- JSON配置预览 -->
        <el-tab-pane label="JSON配置" name="json">
          <div class="json-preview">
            <h4>模板配置JSON</h4>
            <el-input
              :value="jsonConfig"
              type="textarea"
              :rows="20"
              readonly
              class="json-textarea"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TemplatePreview',
  props: {
    template: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      activeTab: 'basic',
      basicData: {},
      tableData: [
        {},
        {}
      ]
    }
  },
  computed: {
    basicFields() {
      return this.template.templateConfig?.basicFields || []
    },
    tableFields() {
      return this.template.templateConfig?.tableFields || []
    },
    excelConfig() {
      return this.template.templateConfig?.excelConfig || {}
    },
    jsonConfig() {
      return JSON.stringify(this.template.templateConfig, null, 2)
    }
  },
  watch: {
    template: {
      handler() {
        this.initPreviewData()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    initPreviewData() {
      // 初始化基础数据示例
      this.basicData = {}
      this.basicFields.forEach(field => {
        this.basicData[field.code] = this.getFieldDefaultValue(field)
      })

      // 初始化表格数据示例
      this.tableData = [
        this.createTableRowData(),
        this.createTableRowData()
      ]
    },

    createTableRowData() {
      const rowData = {}
      this.tableFields.forEach(field => {
        rowData[field.code] = this.getFieldDefaultValue(field)
      })
      return rowData
    },

    getFieldDefaultValue(field) {
      if (field.defaultValue) {
        return field.defaultValue
      }
      
      switch (field.type) {
        case 'text':
        case 'textarea':
          return '示例文本'
        case 'number':
          return 100
        case 'date':
          return '2024-01-15'
        case 'datetime':
          return '2024-01-15 10:30:00'
        case 'select':
        case 'radio':
          return field.options?.[0] || '选项1'
        case 'checkbox':
          return field.options?.slice(0, 1) || ['选项1']
        default:
          return ''
      }
    },

    getFieldPlaceholder(field) {
      switch (field.type) {
        case 'text':
        case 'textarea':
          return field.placeholder || '请输入' + field.name
        case 'number':
          return '数字'
        case 'date':
          return 'YYYY-MM-DD'
        case 'datetime':
          return 'YYYY-MM-DD HH:mm:ss'
        case 'select':
        case 'radio':
          return field.options?.[0] || '选项'
        case 'checkbox':
          return field.options?.join(',') || '选项'
        default:
          return field.name
      }
    },

    getFieldComponent(type) {
      const componentMap = {
        text: 'el-input',
        textarea: 'el-input',
        number: 'el-input-number',
        date: 'el-date-picker',
        datetime: 'el-date-picker',
        select: 'el-select',
        radio: 'el-radio-group',
        checkbox: 'el-checkbox-group'
      }
      return componentMap[type] || 'el-input'
    },

    getFieldProps(field) {
      const props = {
        placeholder: field.placeholder || `请输入${field.name}`
      }

      if (field.type === 'textarea') {
        props.type = 'textarea'
        props.rows = field.rows || 3
      } else if (field.type === 'number') {
        props.min = field.min
        props.max = field.max
        props.step = field.step || 1
        props.precision = field.precision || 0
      } else if (field.type === 'date') {
        props.type = 'date'
        props.format = field.format || 'yyyy-MM-dd'
        props.valueFormat = 'yyyy-MM-dd'
      } else if (field.type === 'datetime') {
        props.type = 'datetime'
        props.format = field.format || 'yyyy-MM-dd HH:mm:ss'
        props.valueFormat = 'yyyy-MM-dd HH:mm:ss'
      } else if (field.type === 'select') {
        props.clearable = field.clearable !== false
        props.filterable = field.filterable || false
      }

      return props
    }
  }
}
</script>

<style lang="scss" scoped>
.template-preview {
  .preview-header {
    padding: 20px;
    border-bottom: 1px solid #e6e6e6;

    h3 {
      margin: 0 0 15px 0;
      color: #333;
    }
  }

  .preview-content {
    padding: 20px;

    h4 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 16px;
    }

    h5 {
      margin: 20px 0 10px 0;
      color: #666;
      font-size: 14px;
    }

    .preview-form {
      background: #fafafa;
      padding: 20px;
      border-radius: 4px;
    }

    .preview-table {
      background: #fafafa;
    }

    .excel-preview {
      .excel-layout-preview {
        margin-top: 20px;

        .excel-mock {
          border: 1px solid #e6e6e6;
          border-radius: 4px;
          background: #fff;
          padding: 15px;

          .excel-section {
            margin-bottom: 20px;

            .section-title {
              font-weight: bold;
              color: #333;
              margin-bottom: 10px;
              padding: 5px 10px;
              background: #f0f0f0;
              border-radius: 3px;
            }

            .excel-rows {
              .excel-row {
                display: flex;
                border-bottom: 1px solid #e6e6e6;

                &:last-child {
                  border-bottom: none;
                }
              }
            }

            .excel-table {
              .excel-header {
                display: flex;
                background: #f5f5f5;
                font-weight: bold;
              }

              .excel-row {
                display: flex;
                border-bottom: 1px solid #e6e6e6;

                &:last-child {
                  border-bottom: none;
                }
              }
            }

            .excel-cell {
              padding: 8px 12px;
              border-right: 1px solid #e6e6e6;
              min-height: 32px;
              display: flex;
              align-items: center;

              &:last-child {
                border-right: none;
              }

              &.label-cell {
                background: #f9f9f9;
                font-weight: 500;
                width: 120px;
                flex-shrink: 0;
              }

              &.value-cell {
                flex: 1;
                color: #999;
              }

              &.header-cell {
                background: #f5f5f5;
                font-weight: bold;
                text-align: center;
              }

              &.data-cell {
                color: #999;
                text-align: center;
              }
            }
          }
        }
      }
    }

    .json-preview {
      .json-textarea {
        font-family: 'Courier New', monospace;
        font-size: 12px;
      }
    }
  }
}
</style>
