<template>
  <div class="excel-uploader">
    <div class="uploader-header">
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="选择模板" />
        <el-step title="上传文件" />
        <el-step title="数据预览" />
        <el-step title="确认导入" />
      </el-steps>
    </div>

    <div class="uploader-content">
      <!-- 步骤1: 选择模板 -->
      <div v-show="currentStep === 0" class="step-content">
        <div class="step-title">
          <h3>选择检测模板</h3>
          <p>请选择要导入数据的检测模板</p>
        </div>
        
        <el-form :model="templateForm" label-width="100px" size="small">
          <el-form-item label="设备类型">
            <el-select
              v-model="templateForm.deviceTypeId"
              placeholder="请选择设备类型"
              @change="handleDeviceTypeChange"
              style="width: 300px"
            >
              <el-option
                v-for="item in deviceTypeOptions"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="检测模板">
            <el-select
              v-model="templateForm.templateId"
              placeholder="请选择检测模板"
              @change="handleTemplateChange"
              style="width: 300px"
            >
              <el-option
                v-for="item in templateOptions"
                :key="item.id"
                :label="item.templateName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <div class="template-info" v-if="selectedTemplate">
          <h4>模板信息</h4>
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="模板名称">{{ selectedTemplate.templateName }}</el-descriptions-item>
            <el-descriptions-item label="模板编码">{{ selectedTemplate.templateCode }}</el-descriptions-item>
            <el-descriptions-item label="设备类型">{{ selectedTemplate.deviceTypeName }}</el-descriptions-item>
            <el-descriptions-item label="版本">{{ selectedTemplate.version }}</el-descriptions-item>
          </el-descriptions>
          
          <div class="template-actions">
            <el-button type="primary" size="small" @click="downloadTemplate">
              <i class="el-icon-download"></i>
              下载模板
            </el-button>
            <el-button type="success" size="small" @click="nextStep">
              下一步
            </el-button>
          </div>
        </div>
      </div>

      <!-- 步骤2: 上传文件 -->
      <div v-show="currentStep === 1" class="step-content">
        <div class="step-title">
          <h3>上传Excel文件</h3>
          <p>请上传填写好的检测数据Excel文件</p>
        </div>

        <el-upload
          ref="upload"
          class="upload-demo"
          drag
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          :show-file-list="false"
          accept=".xlsx,.xls"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件，且不超过10MB</div>
        </el-upload>

        <div class="upload-progress" v-if="uploading">
          <el-progress :percentage="uploadProgress" :status="uploadStatus"></el-progress>
          <p>{{ uploadMessage }}</p>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
        </div>
      </div>

      <!-- 步骤3: 数据预览 -->
      <div v-show="currentStep === 2" class="step-content">
        <div class="step-title">
          <h3>数据预览</h3>
          <p>请检查解析结果，确认数据无误后进行导入</p>
        </div>

        <div class="parse-result" v-if="parseResult">
          <el-alert
            :title="`解析完成：共${parseResult.totalRows}行数据，成功${parseResult.successRows}行，错误${parseResult.errorRows}行`"
            :type="parseResult.errorRows > 0 ? 'warning' : 'success'"
            :closable="false"
            show-icon
          />

          <!-- 错误信息 -->
          <div v-if="parseResult.errors.length > 0" class="error-list">
            <h4>错误信息</h4>
            <el-table :data="parseResult.errors" size="small" max-height="200">
              <el-table-column prop="row" label="行号" width="80" />
              <el-table-column prop="fieldName" label="字段" width="120" />
              <el-table-column prop="value" label="值" width="120" />
              <el-table-column prop="message" label="错误信息" />
            </el-table>
          </div>

          <!-- 基础数据预览 -->
          <div v-if="parseResult.basicData && Object.keys(parseResult.basicData).length > 0" class="basic-data-preview">
            <h4>基础数据</h4>
            <el-descriptions :column="2" size="small" border>
              <el-descriptions-item
                v-for="field in selectedTemplate.templateConfig.basicFields"
                :key="field.code"
                :label="field.name"
              >
                {{ parseResult.basicData[field.code] || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 表格数据预览 -->
          <div v-if="parseResult.tableData.length > 0" class="table-data-preview">
            <h4>表格数据（前10条）</h4>
            <el-table :data="parseResult.tableData.slice(0, 10)" size="small" border>
              <el-table-column
                v-for="field in selectedTemplate.templateConfig.tableFields"
                :key="field.code"
                :prop="field.code"
                :label="field.name"
                :width="field.width"
              />
            </el-table>
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button
            type="primary"
            @click="nextStep"
            :disabled="!parseResult || parseResult.errorRows > 0"
          >
            下一步
          </el-button>
        </div>
      </div>

      <!-- 步骤4: 确认导入 -->
      <div v-show="currentStep === 3" class="step-content">
        <div class="step-title">
          <h3>确认导入</h3>
          <p>请确认导入信息，点击确认后将保存数据到系统中</p>
        </div>

        <div class="import-summary">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="模板名称">{{ selectedTemplate.templateName }}</el-descriptions-item>
            <el-descriptions-item label="文件名称">{{ uploadedFileName }}</el-descriptions-item>
            <el-descriptions-item label="基础数据">{{ Object.keys(parseResult.basicData || {}).length }}项</el-descriptions-item>
            <el-descriptions-item label="表格数据">{{ parseResult.tableData.length }}行</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="confirmImport" :loading="importing">
            确认导入
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDeviceTypeOptions } from '@/api/detection/deviceTypes'
import { getTemplateOptions, getTemplate, exportExcelTemplate } from '@/api/detection/templates'
import { uploadExcelData, confirmSaveData } from '@/api/detection/data'
import { getToken } from '@/utils/auth'
import ExcelUtils from '@/utils/excel-utils'

export default {
  name: 'ExcelUploader',
  data() {
    return {
      currentStep: 0,
      deviceTypeOptions: [],
      templateOptions: [],
      selectedTemplate: null,
      templateForm: {
        deviceTypeId: null,
        templateId: null
      },
      uploading: false,
      uploadProgress: 0,
      uploadStatus: '',
      uploadMessage: '',
      uploadedFileName: '',
      parseResult: null,
      importing: false
    }
  },
  computed: {
    uploadAction() {
      return '/api/detection-data/upload-excel'
    },
    uploadHeaders() {
      return {
        'Authorization': 'Bearer ' + getToken()
      }
    },
    uploadData() {
      return {
        templateId: this.templateForm.templateId
      }
    }
  },
  mounted() {
    this.loadDeviceTypes()
  },
  methods: {
    // 加载设备类型
    async loadDeviceTypes() {
      try {
        const response = await getDeviceTypeOptions()
        this.deviceTypeOptions = response || []
      } catch (error) {
        console.error('加载设备类型失败:', error)
      }
    },

    // 设备类型变化
    handleDeviceTypeChange(deviceTypeId) {
      this.templateForm.templateId = null
      this.selectedTemplate = null
      this.loadTemplateOptions(deviceTypeId)
    },

    // 加载模板选项
    async loadTemplateOptions(deviceTypeId) {
      try {
        const response = await getTemplateOptions(deviceTypeId)
        this.templateOptions = response || []
      } catch (error) {
        console.error('加载模板选项失败:', error)
      }
    },

    // 模板变化
    async handleTemplateChange(templateId) {
      try {
        const response = await getTemplate(templateId)
        this.selectedTemplate = response
      } catch (error) {
        console.error('加载模板详情失败:', error)
      }
    },

    // 下载模板
    async downloadTemplate() {
      try {
        const response = await exportExcelTemplate(this.templateForm.templateId)
        this.$download.saveAs(response, `${this.selectedTemplate.templateName}_模板.xlsx`)
      } catch (error) {
        this.$message.error('下载模板失败')
      }
    },

    // 上传前验证
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                     file.type === 'application/vnd.ms-excel'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过10MB!')
        return false
      }

      this.uploading = true
      this.uploadProgress = 0
      this.uploadStatus = ''
      this.uploadMessage = '正在上传文件...'
      this.uploadedFileName = file.name

      return true
    },

    // 上传成功
    handleUploadSuccess(response) {
      this.uploading = false
      if (response.code === 200) {
        this.parseResult = response.data.parseResult
        this.uploadMessage = '文件上传成功'
        this.nextStep()
      } else {
        this.$message.error(response.msg || '上传失败')
      }
    },

    // 上传失败
    handleUploadError(error) {
      this.uploading = false
      this.uploadStatus = 'exception'
      this.uploadMessage = '文件上传失败'
      this.$message.error('文件上传失败')
    },

    // 确认导入
    async confirmImport() {
      this.importing = true
      try {
        const importData = {
          templateId: this.templateForm.templateId,
          basicData: this.parseResult.basicData,
          tableData: this.parseResult.tableData,
          fileName: this.uploadedFileName
        }
        
        await confirmSaveData(importData)
        this.$message.success('数据导入成功')
        this.$emit('success')
        this.reset()
      } catch (error) {
        this.$message.error('数据导入失败')
      } finally {
        this.importing = false
      }
    },

    // 下一步
    nextStep() {
      if (this.currentStep < 3) {
        this.currentStep++
      }
    },

    // 上一步
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },

    // 重置
    reset() {
      this.currentStep = 0
      this.templateForm = {
        deviceTypeId: null,
        templateId: null
      }
      this.selectedTemplate = null
      this.templateOptions = []
      this.parseResult = null
      this.uploadedFileName = ''
      this.uploading = false
      this.importing = false
    }
  }
}
</script>

<style lang="scss" scoped>
.excel-uploader {
  .uploader-header {
    padding: 20px;
    background: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;
  }

  .uploader-content {
    padding: 30px;

    .step-content {
      min-height: 400px;

      .step-title {
        text-align: center;
        margin-bottom: 30px;

        h3 {
          margin: 0 0 10px 0;
          color: #333;
        }

        p {
          margin: 0;
          color: #666;
          font-size: 14px;
        }
      }

      .template-info {
        margin-top: 30px;
        padding: 20px;
        background: #f9f9f9;
        border-radius: 4px;

        h4 {
          margin: 0 0 15px 0;
          color: #333;
        }

        .template-actions {
          margin-top: 20px;
          text-align: center;

          .el-button {
            margin: 0 10px;
          }
        }
      }

      .upload-demo {
        margin: 20px 0;
      }

      .upload-progress {
        margin: 20px 0;
        text-align: center;

        p {
          margin-top: 10px;
          color: #666;
        }
      }

      .parse-result {
        .error-list,
        .basic-data-preview,
        .table-data-preview {
          margin: 20px 0;

          h4 {
            margin: 0 0 15px 0;
            color: #333;
          }
        }
      }

      .import-summary {
        margin: 20px 0;
      }

      .step-actions {
        margin-top: 30px;
        text-align: center;

        .el-button {
          margin: 0 10px;
        }
      }
    }
  }
}
</style>
