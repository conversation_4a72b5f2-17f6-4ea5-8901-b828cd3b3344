import request from '@/utils/request'

/**
 * 文件上传
 * @param {Object} data
 */
export function uploads(data) {
  return request({
    url: '/file/uploads',
    method: 'post',
    data: data
  })
}

/**
 * 获取文件
 * @param {Object} params
 */
export function getFile(params) {
  return request({
    url: '/file/files',
    method: 'get',
    params: params
  })
}

/**
 * 删除文件
 * @param {Object} params
 */
export function delFile(params) {
  return request({
    url: '/file/delFile',
    method: 'DELETE',
    params: params
  })
}
/**
 * 图片上传
 */
export function uploadsImage(data) {
  return request({
    url: '/file/userDefinedPathUpload',
    method: 'post',
    data: data
  })
}

/**
 * 上传在线文件
 */
export function uploadCloudImage(data, fileId) {
  return request({
    url: fileId ? `/file/fileCloud/upload?fileId=${fileId}`: `/file/fileCloud/upload`,
    method: 'post',
    data: data
  })
}


export function uploads3d(data) {
  return request({
    url: '/file/upload3dModel',
    method: 'post',
    data: data
  })
}

export function uploadFile(data) {
  return request({
    url: '/file/upload',
    method: 'post',
    data: data
  })
}