import request from '@/utils/request'

/**
 * 分页查询设备检测数据列表
 * @param {Object} query 查询参数
 */
export function listDeviceDetectionData(query) {
  return request({
    url: '/iot/deviceDetectionData/pageList',
    method: 'get',
    params: query
  })
}

/**
 * 根据ID查询检测数据详情
 * @param {Number} id 检测数据ID
 */
export function getDeviceDetectionData(id) {
  return request({
    url: `/iot/deviceDetectionData/${id}`,
    method: 'get'
  })
}

/**
 * 上传并解析Excel文件
 * @param {FormData} formData 表单数据
 */
export function uploadExcelFile(formData) {
  return request({
    url: '/iot/deviceDetectionData/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 重新解析Excel文件
 * @param {Number} id 检测数据ID
 */
export function reparseExcelFile(id) {
  return request({
    url: `/iot/deviceDetectionData/reparse/${id}`,
    method: 'post'
  })
}

/**
 * 删除检测数据
 * @param {Number} id 检测数据ID
 */
export function deleteDeviceDetectionData(id) {
  return request({
    url: `/iot/deviceDetectionData/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除检测数据
 * @param {Array} ids 检测数据ID列表
 */
export function deleteBatchDeviceDetectionData(ids) {
  return request({
    url: '/iot/deviceDetectionData/batch',
    method: 'delete',
    data: ids
  })
}

/**
 * 获取解析状态统计
 */
export function getParseStatusStatistics() {
  return request({
    url: '/iot/deviceDetectionData/statistics',
    method: 'get'
  })
}

/**
 * 根据设备编码获取最新的检测数据
 * @param {String} deviceCode 设备编码
 */
export function getLatestDetectionDataByDevice(deviceCode) {
  return request({
    url: `/iot/deviceDetectionData/latest/${deviceCode}`,
    method: 'get'
  })
}

/**
 * 导出检测数据
 * @param {Number} id 检测数据ID
 * @param {String} format 导出格式
 */
export function exportDetectionData(id, format = 'excel') {
  return request({
    url: `/iot/deviceDetectionData/export/${id}`,
    method: 'get',
    params: { format },
    responseType: 'blob'
  })
}

/**
 * 获取支持的文件类型
 */
export function getSupportedFileTypes() {
  return request({
    url: '/iot/deviceDetectionData/supportedFileTypes',
    method: 'get'
  })
}
