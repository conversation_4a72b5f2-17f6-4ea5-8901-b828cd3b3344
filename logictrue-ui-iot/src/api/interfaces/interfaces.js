import request from '@/utils/request';

/**
 * 查询接口列表
 */
export function getInterfacesList() {
  return request({
    url: '/interfaces/magicInterfaces/treeInterfaces',
    method: 'get',
  });
}

/**
 * magic通过id执行接口
 * @param {Object} data
 */
export function executeInterface(data) {
  return request({
    url: `/interfaces/magicInterfaces/execute`,
    method: 'post',
    data: data,
  });
}
export function executeUrl(url, data) {
  return request({
    url: `/interfaces` + url,
    method: 'post',
    data: data,
  });
}

export function executeTask(params, headers) {
  return request({
    url: `/interfaces/magicTask/task/execute`,
    method: 'post',
    params,
    headers,
  });
}
export function executeTaskLog(params) {
  return request({
    url: `/interfaces/magicTask/log`,
    method: 'get',
    params,
  });
}
/**
 * 批量执行接口
 * @param {Object} data
 */
export function executeInterfaceBatch(data) {
  return request({
    url: '/interfaces/magicInterfaces/executes',
    method: 'post',
    data: data,
  });
}

/**
 * 查询接口树
 */
export function getInterfaceTree() {
  return request({
    url: '/interfaces/magicInterfaces/interfaceTree',
    method: 'get',
  });
}

//获取在线组件列表
export function getAssemblyList(params) {
  return request({
    url: '/interfaces/assembly/list',
    method: 'get',
    params,
  });
}

export function previewAssembly(code) {
  return request({
    url: `/interfaces/assembly/preview/${code}`,
    method: 'get',
  });
}

export function exportPdf(data) {
  return request({
    url: `/interfaces/reportExcel/exportPdf`,
    method: 'post',
    data,
    responseType: 'blob',
  });
}

export function getMagicTask() {
  return request({
    url: `/interfaces/magicTask`,
    method: 'get',
  });
}

export function deleteMagicTask(data) {
  return request({
    url: '/interfaces/magicTask',
    method: 'delete',
    data: data,
  });
}
export function saveMagicTask(data) {
  return request({
    url: '/interfaces/magicTask',
    method: 'post',
    data: data,
  });
}
// ------ 初始化任务 START--------------------------------------
export function getMagicInitialization() {
  return request({
    url: `/interfaces/magicInitialization`,
    method: 'get',
  });
}

export function deleteInitialization(data) {
  return request({
    url: '/interfaces/magicInitialization',
    method: 'delete',
    data: data,
  });
}
export function saveInitialization(data) {
  return request({
    url: '/interfaces/magicInitialization',
    method: 'post',
    data: data,
  });
}

export function doTestInitialization(data) {
  return request({
    url: '/interfaces/magicInitialization/doTestInitialization',
    method: 'post',
    data: data,
  });
}
// ------ 初始化任务 END--------------------------------------
export function getMagicSyncData() {
  return request({
    url: `/interfaces/magicSyncData`,
    method: 'get',
  });
}

export function saveMagicSyncData(data) {
  return request({
    url: `/interfaces/magicSyncData`,
    method: 'post',
    data,
  });
}

export function deleteMagicSyncData(data) {
  return request({
    url: `/interfaces/magicSyncData`,
    method: 'DELETE',
    data,
  });
}

export function getPartiesLoginInfo(partieName) {
  return request({
    url: `/interfaces/baseData/sso/getPartiesLoginInfo?partieName=${partieName}`,
    method: 'get',
  });
}

export function getDatasouceTableInfo(params) {
  return request({
    url: '/interfaces/datasource/datasouceTableInfo',
    method: 'get',
    params,
  });
}

export function getPublicKey() {
  return request({
    url: '/system/user/profile/publicKey',
    method: 'get'
  })
}

export function resetUserPassword(data) {
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    data,
  });
}

export function getSetParam(code) {
  return request({
    url: `/interfaces/excel/getSetParam/${code}`,
    method: 'get',
  });
}

// 查询plc链接
export function getMagicPlc() {
  return request({
    url: `/interfaces/magicPlc/datasources`,
    method: 'get',
  });
}

// 新增修改plc连接
export function updateMagicPlc(data) {
  return request({
    url: `/interfaces/magicPlc/datasource`,
    method: 'post',
    data,
  });
}

// 协议类型
export function getExcelSetParam() {
  return request({
    url: `/interfaces/magicPlc/types`,
    method: 'get',
  });
}

// 测试连接
export function checkConnection(data) {
  return request({
    url: `/interfaces/magicPlc/checkConnection`,
    method: 'post',
    data,
  });
}

// 查询key
export function getMagicPlcKeys(params) {
  return request({
    url: `/interfaces/magicPlc/keys`,
    method: 'get',
    params,
  });
}


//查询key的历史值
export function getKeysHistoryData(params) {
  return request({
    url: `/interfaces/magicPlc/getKeysHistoryData`,
    method: 'get',
    params,
  });
}



export function deleteDatasource(id) {
  return request({
    url: `/interfaces/magicPlc/datasource/${id}`,
    method: 'delete',
  });
}

export function getMagicPlcTasks(params) {
  return request({
    url: `/interfaces/magicPlc/tasks`,
    method: 'get',
    params,
  });
}

export function deletePlcTask(id) {
  return request({
    url: `/interfaces/magicPlc/tasks/${id}`,
    method: 'delete',
  });
}

export function updatePlcTasks(data) {
  return request({
    url: `/interfaces/magicPlc/task`,
    method: 'post',
    data,
  });
}

export function getMagicPlcAllKey(params) {
  return request({
    url: `/interfaces/magicPlc/allKeys`,
    method: 'get',
    params,
  });
}

export function getCodeManage(params) {
  return request({
    url: `/interfaces/codeManage`,
    method: 'get',
    params,
  });
}

export function viewCodeManage(codeId) {
  return request({
    url: `/interfaces/codeManage/view/${codeId}`,
    method: 'get',
  });
}

export function getCheckUser() {
  return request({
    url: `/interfaces/DRLine/check/getCheckUser`,
    method: 'get',
  });
}