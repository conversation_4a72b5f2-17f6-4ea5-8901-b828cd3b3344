import { constantRoutes } from '@/router'
// 系统切换路由的混入函数
export default {
  computed: {
    // 所有的路由信息
    routers() {
      return this.$store.state.permission.topbarRouters
    },
    // 设置子路由
    childrenMenus() {
      var childrenMenus = []
      this.routers.map((router) => {
        for (var item in router.children) {
          if (router.children[item].parentPath === undefined) {
            if (router.path === '/') {
              router.children[item].path = '/redirect/' + router.children[item].path
            } else {
              if (!this.ishttp(router.children[item].path)) {
                router.children[item].path = router.path + '/' + router.children[item].path
              }
            }
            router.children[item].parentPath = router.path
          }
          childrenMenus.push(router.children[item])
        }
      })
      return constantRoutes.concat(childrenMenus)
    }
  },
  methods: {
    ishttp(url) {
      return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1
    },
    /**
     * 点击每一个子系统
     * @param {Object} item
     */
    getSystemRoute(item) {
      // 对应子系统的菜单集合
      let routes = []
      let key = item.path

      if (this.childrenMenus && this.childrenMenus.length > 0) {
        this.childrenMenus.map((item) => {
          if (key === item.parentPath || (key === 'index' && item.path === '')) {
            routes.push(item)
          }
        })
      }
      let path = ''
      if (routes.length > 0 ) {
        this.$store.commit('SET_SIDEBAR_ROUTERS', routes)
        path = routes[0].children ? routes[0].path + '/' + routes[0].children[0].path : routes[0].path
      }

      return { routes, key, path }
    }
  }
}
