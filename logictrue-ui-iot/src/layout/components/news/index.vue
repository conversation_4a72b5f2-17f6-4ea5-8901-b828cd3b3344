<template>
  <div>
    <el-popover placement="bottom" trigger="hover">
      <div slot="reference">
        <el-badge
          :value="messageNum"
          :hidden="messageNum == 0"
          class="pointer flash animated infinite"
          style="line-height: 25px; position: absolute; right: 228px"
        />
        <span class="iconfont font-23 pd-r5" style="color: #fff"
          ><i class="el-icon-message"
        /></span>
      </div>
      <el-scrollbar>
        <el-tabs
          v-model="activeName"
          :stretch="true"
          style="width: 380px; height: 250px"
        >
          <el-tab-pane
            :label="'系统消息(' + sysMessage.length + ')'"
            name="first"
          >
            <lt-empty v-if="sysMessage.length === 0" />
            <div
              v-for="(item, index) in sysMessage"
              v-else
              :key="index"
              class="pd-b10 pd-t10 border-bottom"
              @click="openMessge(item, index)"
            >
              <div
                style="height: 30px; font-weight: 600"
                class="d-flex a-center"
              >
                {{ item.title }}
              </div>
              <div class="d-flex a-center j-sb">
                <div style="width: 200px" class="text-ellipsis">
                  {{ item.context }}
                </div>
                <div>{{ item.crateTime }}</div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane
            :label="'通知(' + normalMessage.length + ')'"
            name="second"
          >
            <lt-empty v-if="normalMessage.length === 0" />
            <div
              v-for="(item, index) in normalMessage"
              v-else
              :key="index"
              style="cursor:pointer"
              class="pd-b10 pd-t10 d-flex j-sb border-bottom"
              @click="noticeMessge(item, index)"
            >
              <span style="font-weight: 600" class="text-ellipsis">{{
                item.noticeTitle
              }}</span>
              <span>{{ item.createTime }}</span>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-scrollbar>
    </el-popover>
    <el-dialog
      :title="notice.noticeTitle"
      :visible.sync="dialogVisible"
      width="50%"
      :modal-append-to-body="false"
    >
      <div style="min-height: calc(100vh - 400px)">
        <div style="text-align: center; color: #909399">
          {{ notice.createTime }}
        </div>
        <div style="text-align: center; color: #909399">
          {{ notice.createBy }}
        </div>
        <div v-html="notice.noticeContent" />
      </div>
      <template #footer>
        <el-button
          type="primary"
          :disabled="statas"
          @click="determine(notice.noticeId)"
          >确 定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'News',
  data() {
    return {
      activeName: 'first',
      normalMessage: [],
      sysMessage: [],
      randomString: '',
      dialogVisible: false,
      notice: {},
      index: 0,
      statas: false,
    };
  },
  computed:{
messageNum(){
  return  this.sysMessage.length + this.normalMessage.length;
}
  },
  mounted() {
      this.$store.state.app.webSocket.setOnMessage('news',(data)=>{

            if (data.message.length != 0) {
                this.sysMessage = data.message;
              }
              if (data.notice.length != 0) {
                this.normalMessage = data.notice;
              }

  })


  },
  methods: {

    openMessge(item, index) {

      this.$router.push(item.url);
    },
    /**
     * 通知
     */
    noticeMessge(item, index) {
      this.dialogVisible = true;
      this.statas = false;
      this.notice = item;
      this.index = index;
    },
    /**
     * 确定
     */
    determine(noticeId) {

    },
  },
};
</script>

<style lang="scss" scoped>
.pointer {
  margin: 0 20px;
}
.border-bottom {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}
/**
   * 闪烁效果
   */
.animated {
  -webkit-animation-duration: 6s;
  animation-duration: 6s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.flash {
  -webkit-animation-name: flash;
  animation-name: flash;
}

@-webkit-keyframes flash {
  0%,
  100%,
  50% {
    opacity: 1;
  }

  25%,
  75% {
    opacity: 0.4;
  }
}

@keyframes flash {
  0%,
  100%,
  50% {
    opacity: 1;
  }

  25%,
  75% {
    opacity: 0.4;
  }
}
::v-deep .el-tabs__active-bar.is-top {
  width: 50% !important;
}
</style>
