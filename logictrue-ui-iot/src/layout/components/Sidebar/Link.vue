<template>
  <component :is="type" v-bind="linkProps(to)" @click="linkClick(to)">
    <slot />
  </component>
</template>

<script>
import { isExternal } from '@/utils/validate';

export default {
  inject: ['reload'],
  props: {
    to: {
      type: String,
      required: true,
    },
  },
  computed: {
    isExternal() {
      return isExternal(this.to);
    },
    type() {
      if (this.isExternal) {
        return 'a';
      }
      return 'router-link';
    },
  },
  methods: {
    linkProps(to) {
      if (this.isExternal) {
        return {
          href: to,
          target: '_blank',
          rel: 'noopener',
        };
      }
      if(to.includes('workStation')){
        to='/workStation'
      }
      if(to.includes('schedule')){
        to='/schedule'
      }
      if(to.includes('workshop')){
        to='/workshop'
      }
      return {
        to:  to,
      };
    },
    /**
     * 点击进行路由跳转
     * @param {Object} to
     */
    linkClick(to) {
      // 判断是否点击的为当前路由,如果是则刷新当前路由
      if (sessionStorage.getItem('lastRoute') === to && to !== '/tool/build') {
        // 刷新界面
        this.$router.replace({
          path: '/redirect' + to,
        });
        return;
      }

      sessionStorage.setItem('lastRoute', to);
    },
  },
};
</script>
