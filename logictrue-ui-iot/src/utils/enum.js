export const errorCode = {
  401: '认证失败，无法访问系统资源',
  403: '当前操作没有权限',
  404: '访问资源不存在',
  default: '系统未知错误,请反馈给管理员',
};

export const timeGrain = [
  { label: '日', value: 'day', util: '时' },
  { label: '月', value: 'month', util: '日' },
  { label: '年', value: 'year', util: '月' },
];

export const singleDict = {
  energy_electric: [
    {
      label: '用电分析',
      value: 'energy_electric_quantity',
      children: [
        { label: '同比环比', value: 1 },
        { label: '分时段', value: 2 },
      ],
    },
    { label: '电压信息', value: 'energy_critical_voltage' },
    { label: '电流信息', value: 'energy_critical_current' },
    //{ label: '功率因素分析', value: 'energy_electric_power' },
    { label: '负荷分析', value: 'energy_electric_load' },
  ],
};

export const contrastDict = [
  { dictLabel: '等于', dictCode: 'eq' },
  { dictLabel: '不等于', dictCode: 'ne' },
  { dictLabel: '大于', dictCode: 'gt' },
  { dictLabel: '大于等于', dictCode: 'ge' },
  { dictLabel: '小于', dictCode: 'lt' },
  { dictLabel: '小于等于', dictCode: 'le' },
];

export const circuitDict = [
  { dictLabel: '主路', dictCode: true },
  { dictLabel: '支路', dictCode: false },
];

export const withDrawalBusinessType = [
  { dictLabel: '备品备件领用', dictCode: 1 },
  { dictLabel: '备品备件退回', dictCode: 2 },
];

export const libraryBusinessType = [
  { dictLabel: '备品备件出库', dictCode: 3 },
  { dictLabel: '备品备件入库', dictCode: 4 },
];

export const stockType = [
  { dictLabel: '库存数量充足', dictCode: 1 },
  { dictLabel: '库存数量不足', dictCode: 2 },
];

export const partLevel = [
  { dictLabel: '一级', dictCode: 1 },
  { dictLabel: '二级', dictCode: 2 },
  { dictLabel: '三级', dictCode: 3 },
];

// 图片的文件类型
export const imgFormat = [
  'jpg',
  'jpeg',
  'png',
  'gif',
  'bmp',
  'jpg',
  'jpeg',
  'bmp',
  'tif',
];

// 视频的文件类型
export const videoFormat = [
  'rm',
  'wmv',
  'avi',
  'mp4',
  '3gp',
  'mkv',
  'rmvb',
  'ogv',
];

// 时间单位
export const timeUnit = [
  { dictLabel: '日', dictCode: 1 },
  { dictLabel: '周', dictCode: 2 },
  { dictLabel: '月', dictCode: 3 },
  { dictLabel: '季', dictCode: 4 },
  { dictLabel: '年', dictCode: 5 },
];

export const processCondition = [
  {
    label: '等于',
    value: 'EQ',
  },
  {
    label: '不等于',
    value: 'NE',
  },
  {
    label: '包含',
    value: 'LIKE',
  },
  {
    label: '大于',
    value: 'GT',
  },
  {
    label: '大于等于',
    value: 'GE',
  },
  {
    label: '小于',
    value: 'LT',
  },
  {
    label: '小于等于',
    value: 'LE',
  },
];

// 设备状态类型
export const deviceStatus = [
  { statusLabel: '停用', status: '0' },
  { statusLabel: '启用', status: '1' },
  { statusLabel: '维修中', status: '2' },
  { statusLabel: '保养中', status: '3' },
];

export const apiTypeDict = [
  { dictLabel: '前置回调', dictCode: 'before' },
  { dictLabel: '后置回调', dictCode: 'after' },
  { dictLabel: '驳回回调', dictCode: 'reject' },
  { dictLabel: '撤回回调', dictCode: 'withdraw' },
];
export const formTypeDict = [
  { dictLabel: '表单', dictCode: 1 },
  { dictLabel: '视图', dictCode: 2 },
  { dictLabel: 'APP', dictCode: 3 },
];
export const labelFix = [
  { dictLabel: '左', dictCode: 'left' },
  { dictLabel: '无', dictCode: 'no' },
  { dictLabel: '右', dictCode: 'right' },
];

export const alignFix = [
  { dictLabel: '左', dictCode: 'left' },
  { dictLabel: '中', dictCode: 'center' },
  { dictLabel: '右', dictCode: 'right' },
];

export const numberingTypeDict = [
  { label: 'yyyyMMdd', value: 'yyyyMMdd' },
  { label: 'yyyyMM', value: 'yyyyMM' },
  { label: 'yyyy', value: 'yyyy' },
];
export const deptType = [
  { dictLabel: '基地', dictCode: 261 },
  { dictLabel: '工序', dictCode: 263 },
];

export const labelWidth = {
  column: 10, // 列数
  width: 40, // 列宽
};
// 打印纸尺寸大小
// 打印纸尺寸大小
// width: 435,
// height: 339
export const TemplateSize = [
  {
    dictLabel: '卷-435*340',
    dictCode: 1,
    width: 435,
    height: 339, // 适配打印机的纸张，需要在原高度上减一
  },
];
// 业务类型
export const SpareUseType = [
  {
    label: '备品备件出入库',
    value: '1',
  },
  {
    label: '备品备件领退',
    value: '2',
  },
];
// 使用类型备品备件出库入库
export const SpareUseTypeStoreList = [
  {
    label: '备品备件出库',
    value: '1',
  },
  {
    label: '备品备件入库',
    value: '2',
  },
];
// 使用类型备品备件领退
export const SpareUseTypeRetreatList = [
  {
    label: '备品备件领用',
    value: '1',
  },
  {
    label: '备品备件退用',
    value: '2',
  },
];

export const paperSizeTypeList = [
  { label: '信纸', value: 1 },
  { label: '小纸信', value: 2 },
  { label: 'Tabloid', value: 3 },
  { label: 'PAPER_LEDGER', value: 4 },
  { label: '法律专用纸', value: 5 },
  { label: 'STATEMENT', value: 6 },
  { label: 'EXECUTIVE', value: 7 },
  { label: 'A3', value: 8 },
  { label: 'A4', value: 9 },
  { label: 'A4_small', value: 10 },
  { label: 'A5', value: 11 },
  { label: 'B4', value: 12 },
  { label: 'B5', value: 13 },
  { label: 'P80列', value: 45, feetWidth: 9.5, feetHigh: 11 },
  { label: 'P40列', value: -45, feetWidth: 10, feetHigh: 5.5 },
];
