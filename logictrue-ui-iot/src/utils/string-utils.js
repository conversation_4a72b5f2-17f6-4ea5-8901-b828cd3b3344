import ChinesePY from './chinesePY.js';
import { handleWidgetList } from './array-utils';

String.prototype.toInitial = function () {
  let result = '';
  let reg = /[\u4e00-\u9fa5]+/;
  this.split('').forEach((item) => {
    if (item) {
      if (reg.test(item)) {
        result += ChinesePY.getWordsCode(item);
      } else {
        result += item;
      }
    }
  });
  return result.substr(0, 6);
};

// 返回唯一标识
export function getUUID(length = 32) {
  let str = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  for (let i = length; i > 0; --i)
    result += str[Math.floor(Math.random() * str.length)];

  return result;
}

/**
 * 数据模板字符串替换
 * @param {*} data { widgetList: [], ...... }
 */
export function dataTemplateReplace(data) {
  let temp = JSON.stringify(data);

  // - 转 驼峰
  const handler = (str) => {
    let feng = ``;
    let arr = str.split(`-`);
    let newArr = arr.map((ele, idx) => {
      return idx === 0 ? ele : ele[0].toUpperCase() + ele.slice(1);
    });
    feng = newArr.join(``);
    return feng;
  };

  handleWidgetList(data.widgetList, (item) => {
    // 替换掉id
    if ('id' in item) {
      let target = item.id;
      target = handler(item.type) + getUUID(8);
      temp = temp.replace(new RegExp(item.id, 'g'), target);
    }
  });

  return temp;
}

/**
 * 处理链接中的占位符
 * @param {*} link
 * @returns
 */
export function handlerLink(link) {
  if (link && link.includes('${current}')) {
    const getRootPath = () => {
      //获取当前网址，
      var curPath = window.document.location.href;
      //获取主机地址之后的目录，
      var pathName = window.document.location.pathname;
      var pos = curPath.indexOf(pathName);
      //获取主机地址
      var localhostPaht = curPath.substring(0, pos);
      //获取带"/"的项目名，
      var projectName = pathName.substring(
        0,
        pathName.substr(1).indexOf('/') + 1,
      );
      return localhostPaht;
    };
    let temp = link.split('${current}');
    link = getRootPath() + temp[1];
    return link;
  }
  return link;
}
