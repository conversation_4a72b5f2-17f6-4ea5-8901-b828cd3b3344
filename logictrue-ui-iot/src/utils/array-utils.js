/**
 * 数组对象去重
 * @param {Object} person
 * @param {Object} key
 */
export function reduce(person, key) {
  const obj = {};
  person = person.reduce((cur, next) => {
    obj[next[key]] ? '' : (obj[next[key]] = true && cur.push(next));
    return cur;
  }, []);
  return person;
}

/**
 * 将路由树转换path
 * @param {Object} routes
 */
export function route2PathArray(routes, arr, parent) {
  routes.forEach((route) => {
    if (parent) {
      route.path = parent.path + '/' + route.path;
    }
    if (route.children && route.children.length > 0) {
      route2PathArray(route.children, arr, route);
    }
    arr.push(route);
  });
}

/**
 * 分组,等同于mysql的分组函数
 * @param {Object} array
 * @param {Object} name
 */
export function groupBy(array, name) {
  const groups = {};
  array.forEach(function (o) {
    if (o[name] === '') {
      o[name] = Math.random();
    }
    const group = JSON.stringify(o[name]);
    groups[group] = groups[group] || [];
    groups[group].push(o);
  });
  return Object.keys(groups).map(function (group) {
    return groups[group];
  });
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  const config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children',
  };

  var childrenListMap = {};
  var nodeIds = {};
  var tree = [];

  for (const d of data) {
    const parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentId].push(d);
  }

  for (const d of data) {
    const parentId = d[config.parentId];
    if (nodeIds[parentId] == null) {
      tree.push(d);
    }
  }

  for (const t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]];
    }
    if (o[config.childrenList]) {
      for (const c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  }
  return tree;
}

/**
 * 回显数据字典
 * @param {Object} datas
 * @param {Object} value
 */
export function selectDictLabel(datas, value) {
  var actions = [];
  Object.keys(datas).some((key) => {
    if (datas[key].dictValue === '' + value) {
      actions.push(datas[key].dictLabel);
      return true;
    }
  });
  return actions.join('');
}

/**
 * 前端分页
 * @param {Object} sourceData   源数据
 * @param {Object} size         一页多少条
 * @param {Object} current      页码
 */
export function handlePage(sourceData, size, current) {
  const start = (this.current - 1) * this.size;
  const end = this.current * this.size;

  return sourceData.slice(start, end);
}

/**
 * 数组维度转换
 * @param {*} arr
 * @param {*} number
 * @returns
 */
export function arrConvertDimension(arr, number = 2) {
  var arr2 = [];
  let len = arr.length;
  for (let i = 0, j = 0; i < len; i += number, j++) {
    arr2[j] = arr.splice(0, number);
  }
  return arr2;
}

/**
 * 树结构扁平化
 * @param tree
 */
export function treeToArray(tree, key = 'children') {
  let res = [];
  for (const item of tree) {
    const { [key]: children, ...i } = item;
    if (children && children.length) {
      res = res.concat(treeToArray(children, key));
    }
    res.push(i);
  }
  return res;
}

/**
 * 视图模板组件树处理
 * @param {*} data
 * @param {*} callback
 */
export function handleWidgetList(data, callback) {
  const bfs = (root, callback) => {
    root.forEach((item) => {
      callback && callback(item);

      // 向下递归
      // keys.forEach(key => {
      //   if(key in item && item[key] && item[key].length) bfs(item[key], keys, callback)
      // })
      if ('widgetList' in item && item['widgetList'].length)
        bfs(item['widgetList'], callback);
      if ('cols' in item && item['cols'].length) bfs(item['cols'], callback);
      if ('tabs' in item && item['tabs'].length) bfs(item['tabs'], callback);
    });
  };

  bfs(data, callback);
}

export function handleDFS(data, key, callback) {
  if (data && data.length) {
    data.forEach((item) => {
      callback && callback(item);
      if (item[key] && item[key].length) handleDFS(item[key], key, callback);
    });
  }
}
