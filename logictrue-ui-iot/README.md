# IoT设备检测数据管理系统

这是一个精简版的IoT设备检测数据管理系统，专门用于管理设备检测数据和模板配置。

## 功能特性

- 🔍 **检测数据管理** - 上传、查看和管理设备检测数据
- 📊 **Excel模板设计** - 可视化的Excel模板设计器
- 🔗 **设备模板绑定** - 管理设备与检测模板的绑定关系
- 📤 **数据导入导出** - 支持Excel格式的数据导入导出
- 🎨 **响应式界面** - 基于Element UI的现代化界面

## 技术栈

- **前端框架**: Vue 2.6.12
- **UI组件库**: Element UI 2.15.6
- **路由管理**: Vue Router 3.4.9
- **状态管理**: Vuex 3.6.0
- **HTTP客户端**: Axios 0.21.0
- **Excel处理**: XLSX 0.18.5
- **构建工具**: Vue CLI 4.4.6

## 快速开始

### 环境要求

- Node.js >= 12.0.0
- npm >= 6.0.0

### 安装依赖

```bash
npm install
```

### 开发环境启动

```bash
npm run dev
```

项目将在 http://localhost:9981 启动

### 生产环境构建

```bash
npm run build:prod
```

### 预发布环境构建

```bash
npm run build:stage
```

## 项目结构

```
logictrue-ui-iot/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口
│   │   ├── analysis/      # 设备分析相关API
│   │   └── system/        # 系统相关API
│   ├── assets/            # 资源文件
│   │   └── icons/         # SVG图标
│   ├── components/        # 公共组件
│   │   ├── DictTag/       # 字典标签组件
│   │   └── detection/     # 检测相关组件
│   ├── layout/            # 布局组件
│   ├── router/            # 路由配置
│   ├── store/             # Vuex状态管理
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   │   ├── detection/     # 检测数据相关页面
│   │   │   ├── data/      # 数据管理
│   │   │   └── template/  # 模板管理
│   │   └── error/         # 错误页面
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── babel.config.js        # Babel配置
├── package.json           # 项目配置
└── vue.config.js          # Vue CLI配置
```

## 主要页面

### 检测数据管理
- `/detection/data` - 检测数据列表
- `/detection/data/upload` - 数据上传
- `/detection/data/detail/:id` - 数据详情

### 模板管理
- `/detection/template/excel-template-list` - Excel模板列表
- `/detection/template/excel-designer` - Excel模板设计器
- `/detection/template/device-template-binding` - 设备模板绑定

## 开发说明

### API配置

项目使用环境变量配置API地址：
- 开发环境: `VUE_APP_BASE_API = '/dev-api'`
- 生产环境: `VUE_APP_BASE_API = '/prod-api'`

### 代理配置

开发环境下，API请求会代理到 `http://127.0.0.1:8080`

### 样式说明

项目使用SCSS作为CSS预处理器，支持Element UI主题定制。

## 部署说明

1. 执行构建命令生成dist目录
2. 将dist目录部署到Web服务器
3. 配置反向代理将API请求转发到后端服务

## 许可证

MIT License
